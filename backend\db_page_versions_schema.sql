-- PostgreSQL schema for page versions
-- This table stores all versions of individual pages to track changes over time
-- Extends the existing prototype versioning system to page-level granularity

CREATE TABLE IF NOT EXISTS page_versions (
    id SERIAL PRIMARY KEY,
    page_id INTEGER NOT NULL REFERENCES prototype_pages(id) ON DELETE CASCADE,
    version_number INTEGER NOT NULL,
    html_content TEXT NOT NULL,              -- HTML content for this version
    css_content TEXT,                        -- CSS content for this version (if any)
    change_description TEXT,                 -- Description of what changed in this version
    creation_method VARCHAR(50) NOT NULL,    -- 'generated', 'manual_edit', 'patch', 'restored'
    user_prompt TEXT,                        -- The user's request that led to this version
    llm_response TEXT,                       -- The full LLM response (if applicable)
    file_size INTEGER,                       -- Size of HTML content in bytes
    character_count INTEGER,                 -- Character count of HTML content
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Ensure unique version numbers per page
    UNIQUE(page_id, version_number)
);

-- Index for efficient querying of versions by page
CREATE INDEX IF NOT EXISTS idx_page_versions_page_id ON page_versions(page_id, version_number DESC);

-- Index for querying by creation method
CREATE INDEX IF NOT EXISTS idx_page_versions_creation_method ON page_versions(creation_method);

-- Index for querying recent versions
CREATE INDEX IF NOT EXISTS idx_page_versions_created_at ON page_versions(created_at DESC);

-- Function to automatically increment version number for pages
CREATE OR REPLACE FUNCTION get_next_page_version_number(p_page_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    next_version INTEGER;
BEGIN
    SELECT COALESCE(MAX(version_number), 0) + 1 
    INTO next_version 
    FROM page_versions 
    WHERE page_id = p_page_id;
    
    RETURN next_version;
END;
$$ LANGUAGE plpgsql;

-- Function to create a new page version
CREATE OR REPLACE FUNCTION create_page_version(
    p_page_id INTEGER,
    p_html_content TEXT,
    p_css_content TEXT DEFAULT NULL,
    p_change_description TEXT DEFAULT NULL,
    p_creation_method VARCHAR(50) DEFAULT 'manual_edit',
    p_user_prompt TEXT DEFAULT NULL,
    p_llm_response TEXT DEFAULT NULL
)
RETURNS INTEGER AS $$
DECLARE
    new_version_number INTEGER;
    new_version_id INTEGER;
    content_file_size INTEGER;
    content_char_count INTEGER;
BEGIN
    -- Get the next version number
    new_version_number := get_next_page_version_number(p_page_id);
    
    -- Calculate content metrics
    content_file_size := LENGTH(p_html_content::bytea);
    content_char_count := LENGTH(p_html_content);
    
    -- Insert the new version
    INSERT INTO page_versions (
        page_id,
        version_number,
        html_content,
        css_content,
        change_description,
        creation_method,
        user_prompt,
        llm_response,
        file_size,
        character_count
    ) VALUES (
        p_page_id,
        new_version_number,
        p_html_content,
        p_css_content,
        p_change_description,
        p_creation_method,
        p_user_prompt,
        p_llm_response,
        content_file_size,
        content_char_count
    ) RETURNING id INTO new_version_id;
    
    RETURN new_version_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get all versions for a page
CREATE OR REPLACE FUNCTION get_page_versions(p_page_id INTEGER)
RETURNS TABLE (
    id INTEGER,
    version_number INTEGER,
    change_description TEXT,
    creation_method VARCHAR(50),
    file_size INTEGER,
    character_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pv.id,
        pv.version_number,
        pv.change_description,
        pv.creation_method,
        pv.file_size,
        pv.character_count,
        pv.created_at
    FROM page_versions pv
    WHERE pv.page_id = p_page_id
    ORDER BY pv.version_number DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to get a specific page version
CREATE OR REPLACE FUNCTION get_page_version(p_page_id INTEGER, p_version_number INTEGER)
RETURNS TABLE (
    id INTEGER,
    version_number INTEGER,
    html_content TEXT,
    css_content TEXT,
    change_description TEXT,
    creation_method VARCHAR(50),
    user_prompt TEXT,
    llm_response TEXT,
    file_size INTEGER,
    character_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        pv.id,
        pv.version_number,
        pv.html_content,
        pv.css_content,
        pv.change_description,
        pv.creation_method,
        pv.user_prompt,
        pv.llm_response,
        pv.file_size,
        pv.character_count,
        pv.created_at
    FROM page_versions pv
    WHERE pv.page_id = p_page_id 
    AND pv.version_number = p_version_number;
END;
$$ LANGUAGE plpgsql;

-- Function to get the latest version number for a page
CREATE OR REPLACE FUNCTION get_latest_page_version_number(p_page_id INTEGER)
RETURNS INTEGER AS $$
DECLARE
    latest_version INTEGER;
BEGIN
    SELECT COALESCE(MAX(version_number), 0)
    INTO latest_version 
    FROM page_versions 
    WHERE page_id = p_page_id;
    
    RETURN latest_version;
END;
$$ LANGUAGE plpgsql;

-- End of page versions schema.
