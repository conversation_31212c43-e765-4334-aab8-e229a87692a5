const express = require('express');
const router = express.Router();
const llmControllerV3 = require('../controllers/llmControllerV3');
const versionService = require('../services/versionService');

/**
 * V3 LLM Routes - Clean implementation based on Readdy.ai approach
 */

// Step 1: Generate intent from element click (like Readdy's /api/page_gen/generate_intent)
router.post('/generate-intent', llmControllerV3.generateIntent);

// Step 2: Implement functionality (inline/modal/page) with server-side prompts
router.post('/implement', llmControllerV3.implementFeature);

// Generate complete HTML from prompt
router.post('/generate-html', llmControllerV3.generateHTML);

// Enhance user prompt with detailed instructions (NEW)
router.post('/enhance-prompt', llmControllerV3.enhancePrompt);

// Edit existing HTML with targeted changes (Readdy.ai style) - now optimized with database lookup
router.post('/edit', llmControllerV3.editHTML);

// Generate structured plan from prompt (for plan review page)
router.post('/plan', llmControllerV3.generatePlan);

// Generate streaming plan (for chat)
router.post('/plan/stream', llmControllerV3.generateStreamingPlan);

// Generate code from plan
router.post('/generate', llmControllerV3.generateCode);

// Version management routes
router.get('/versions/:prototypeId', async (req, res, next) => {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prototypeId } = req.params;
    const { limit = 10, offset = 0 } = req.query;

    const versions = await versionService.getVersionHistory(
      parseInt(prototypeId),
      parseInt(limit),
      parseInt(offset)
    );

    res.json({ versions });
  } catch (error) {
    console.error('Error getting versions:', error);
    next(error);
  }
});

router.get('/versions/:prototypeId/:versionNumber', async (req, res, next) => {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prototypeId, versionNumber } = req.params;

    const version = await versionService.getVersion(
      parseInt(prototypeId),
      parseInt(versionNumber)
    );

    if (!version) {
      return res.status(404).json({ error: 'Version not found' });
    }

    res.json({ version });
  } catch (error) {
    console.error('Error getting version:', error);
    next(error);
  }
});

router.get('/versions/:prototypeId/stats', async (req, res, next) => {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prototypeId } = req.params;

    const stats = await versionService.getVersionStats(parseInt(prototypeId));

    res.json({ stats });
  } catch (error) {
    console.error('Error getting version stats:', error);
    next(error);
  }
});

// Create a new prototype version
router.post('/versions/:prototypeId', async (req, res, next) => {
  try {
    if (!req.session) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prototypeId } = req.params;
    const {
      html,
      css = null,
      change_description = null,
      operation_type = 'manual_edit',
      user_prompt = null,
      llm_response = null
    } = req.body;

    if (!html) {
      return res.status(400).json({ error: 'HTML content is required' });
    }

    // Validate operation_type
    const validOperationTypes = ['generation', 'manual_edit', 'restoration'];
    if (!validOperationTypes.includes(operation_type)) {
      return res.status(400).json({ error: 'Invalid operation_type' });
    }

    console.log(`📝 Creating prototype version for prototype ${prototypeId}:`, {
      operation_type,
      change_description,
      htmlLength: html.length,
      hasUserPrompt: !!user_prompt,
      hasLlmResponse: !!llm_response
    });

    const versionService = require('../services/versionService');
    const versionId = await versionService.createVersion(
      parseInt(prototypeId),
      html,
      css,
      change_description,
      operation_type,
      user_prompt,
      llm_response
    );

    console.log(`✅ Prototype version created successfully: ${versionId}`);

    res.json({
      success: true,
      versionId,
      message: 'Prototype version created successfully'
    });
  } catch (error) {
    console.error('❌ Error creating prototype version:', error);
    next(error);
  }
});

module.exports = router;
