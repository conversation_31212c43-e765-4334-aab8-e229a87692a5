const { ChatOpenAI } = require('@langchain/openai');
const { ChatAnthropic } = require('@langchain/anthropic');
const { HumanMessage, SystemMessage } = require('@langchain/core/messages');
const prompts = require('../config/prompts');
const versionService = require('./versionService');
const DiffService = require('./diffService');
const htmlMergeService = require('./htmlMergeService');
const fs = require('fs');
const path = require('path');



/**
 * V3 LLM Service - Clean implementation based on Readdy.ai approach
 * Focuses on sophisticated prompting and context management for accurate editing
 */

class LLMServiceV3 {
  constructor() {
    this.providers = {
      openai: process.env.OPENAI_API_KEY,
      anthropic: process.env.ANTHROPIC_API_KEY,
      litellm: process.env.LITELLM_API_KEY || 'sk-1234', // LiteLLM proxy often uses dummy key
      deepseek: process.env.DEEPSEEK_API_KEY,
      openrouter: process.env.OPENROUTER_API_KEY
    };

    // Initialize diff service for efficient HTML updates
    this.diffService = new DiffService();

    // LiteLLM proxy configuration
    this.litellmConfig = {
      baseURL: process.env.LITELLM_BASE_URL || 'http://localhost:4000', // Default LiteLLM proxy URL
      apiKey: this.providers.litellm
    };

    // Model mapping for different providers and tasks
    this.modelMapping = {
      // LiteLLM proxy models (Primary - cost-effective via proxy) - Optimized for speed
      litellm: {
        'intent-analysis': 'deepseek-chat', // Fast DeepSeek chat model via LiteLLM
        'planning': 'deepseek-chat', // Fast DeepSeek chat for planning via LiteLLM
        'code-generation': 'deepseek-chat', // DeepSeek chat via LiteLLM (switched from coder)
        'editing': 'deepseek-chat', // DeepSeek chat for precise editing via LiteLLM (switched from coder)
        'context-analysis': 'deepseek-chat', // DeepSeek chat for context via LiteLLM
        'general': 'deepseek-chat'
      },
      // DeepSeek models (Direct API - primary provider)
      deepseek: {
        'intent-analysis': 'deepseek-chat', // DeepSeek chat for fast analysis
        'planning': 'deepseek-reasoner', // DeepSeek reasoning for complex planning
        'code-generation': 'deepseek-chat', // DeepSeek chat for code generation
        'editing': 'deepseek-chat', // DeepSeek chat for precise editing
        'context-analysis': 'deepseek-chat', // DeepSeek chat for context
        'general': 'deepseek-chat'
      },
      // OpenRouter models (Best available models) - Optimized for quality
      openrouter: {
        'intent-analysis': 'anthropic/claude-3.5-sonnet', // Best reasoning model
        'planning': 'anthropic/claude-3.5-sonnet', // Best for complex planning
        'code-generation': 'anthropic/claude-3.5-sonnet', // Excellent for code generation
        'editing': 'anthropic/claude-3.5-sonnet', // Precise editing capabilities
        'context-analysis': 'anthropic/claude-3.5-sonnet', // Superior context understanding
        'general': 'anthropic/claude-3.5-sonnet'
      },
      // OpenAI models (when available)
      openai: {
        'intent-analysis': 'gpt-4o',
        'planning': 'gpt-4o',
        'code-generation': 'gpt-4o',
        'editing': 'gpt-4o',
        'context-analysis': 'gpt-4o',
        'general': 'gpt-4o'
      },
      // Anthropic models
      anthropic: {
        'intent-analysis': 'claude-3-5-sonnet-20241022',
        'planning': 'claude-3-5-sonnet-20241022',
        'code-generation': 'claude-3-5-sonnet-20241022',
        'editing': 'claude-3-5-sonnet-20241022',
        'context-analysis': 'claude-3-5-sonnet-20241022',
        'general': 'claude-3-5-sonnet-20241022'
      }
    };
  }

  /**
   * Get the best available provider based on configuration
   */
  getBestProvider() {
    // FORCE LiteLLM proxy only (Railway deployment)
    if (process.env.LITELLM_BASE_URL && process.env.LITELLM_API_KEY) {
      console.log('🎯 FORCING LiteLLM proxy as primary provider (Railway deployment)');
      console.log('🔗 LiteLLM URL:', process.env.LITELLM_BASE_URL);
      console.log('🔑 LiteLLM API key available:', !!process.env.LITELLM_API_KEY);
      return 'litellm';
    }

    // Log what providers are available for debugging
    console.log('🔍 Available providers:', {
      litellm: !!(process.env.LITELLM_BASE_URL && process.env.LITELLM_API_KEY),
      deepseek: !!this.providers.deepseek,
      openrouter: !!this.providers.openrouter,
      anthropic: !!this.providers.anthropic,
      openai: !!this.providers.openai
    });

    // Force error if LiteLLM is not configured
    console.log('❌ LiteLLM not available, but forcing error to debug');
    throw new Error('LiteLLM proxy not configured. Please set LITELLM_BASE_URL and LITELLM_API_KEY environment variables.');
  }

  /**
   * Get optimal temperature for task type
   */
  getTemperatureForTask(taskType) {
    const temperatureMap = {
      'intent-analysis': 0.2,    // Precise, consistent analysis
      'planning': 0.3,           // Structured, logical thinking
      'code-generation': 0.1,    // Deterministic, working code
      'context-analysis': 0.4,   // Balanced understanding
      'editing': 0.2,            // Balanced: Precise but flexible enough for document preservation
      'general': 0.7             // Default creative temperature
    };

    return temperatureMap[taskType] || 0.7;
  }

  /**
   * Create LLM instance with intelligent model selection based on task
   */
  createLLM(provider = null, streaming = true, taskType = 'general') {
    // Auto-select best provider if not specified
    const selectedProvider = provider || this.getBestProvider();
    const providerKey = selectedProvider.toLowerCase();

    // Get the appropriate model for this task and provider
    const modelName = this.modelMapping[providerKey]?.[taskType] || this.modelMapping[providerKey]?.['general'];

    // Get optimal temperature for this task
    const temperature = this.getTemperatureForTask(taskType);

    if (!modelName) {
      throw new Error(`No model mapping found for provider ${providerKey} and task ${taskType}`);
    }

    console.log(`🤖 Using ${providerKey} provider with model ${modelName} for task: ${taskType} (temp: ${temperature})`);

    switch (providerKey) {
      case 'litellm':
        if (!process.env.LITELLM_BASE_URL || !process.env.LITELLM_API_KEY) {
          throw new Error('LiteLLM proxy not configured. Set LITELLM_BASE_URL and LITELLM_API_KEY');
        }

        return new ChatOpenAI({
          apiKey: process.env.LITELLM_API_KEY,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming,
          maxTokens: 8192, // Increase token limit for complete responses
          timeout: 300000, // 5 minutes timeout for long responses
          configuration: {
            baseURL: process.env.LITELLM_BASE_URL + '/v1' // LiteLLM proxy endpoint
          }
        });

      case 'deepseek':
        if (!this.providers.deepseek) {
          throw new Error('DeepSeek API key not configured');
        }

        return new ChatOpenAI({
          apiKey: this.providers.deepseek,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming,
          configuration: {
            baseURL: 'https://api.deepseek.com/v1' // DeepSeek API endpoint
          }
        });

      case 'openrouter':
        if (!this.providers.openrouter) {
          throw new Error('OpenRouter API key not configured');
        }

        return new ChatOpenAI({
          apiKey: this.providers.openrouter,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming,
          configuration: {
            baseURL: 'https://openrouter.ai/api/v1' // OpenRouter API endpoint
          }
        });

      case 'openai':
        if (!this.providers.openai) {
          throw new Error('OpenAI API key not configured');
        }

        return new ChatOpenAI({
          apiKey: this.providers.openai,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming
        });

      case 'anthropic':
        if (!this.providers.anthropic) {
          throw new Error('Anthropic API key not configured');
        }

        return new ChatAnthropic({
          apiKey: this.providers.anthropic,
          modelName: modelName,
          temperature: temperature,
          streaming: streaming
        });

      default:
        throw new Error(`Unsupported provider: ${providerKey}`);
    }
  }

  /**
   * Send SSE event (ReadyAI style)
   */
  sendSSEEvent(res, event, data) {
    res.write(`event:${event}\n`);
    res.write(`data:${data}\n\n`);
  }

  /**
   * Send ReadyAI-style batched SSE event with multiple complete HTML lines
   * Each line becomes a separate data: line (exactly like ReadyAI)
   */
  sendReadyAIStyleEvent(res, htmlLines) {
    console.log(`📡 [SSE] Sending ReadyAI-style event with ${htmlLines.length} data lines:`);
    res.write(`event:data\n`);
    htmlLines.forEach((htmlLine, index) => {
      // Each complete HTML line becomes a separate data: line
      console.log(`📡 [SSE]   data:${htmlLine.substring(0, 60)}...`);
      res.write(`data:${htmlLine}\n`);
    });
    res.write(`\n`); // Empty line to end the event
    console.log(`📡 [SSE] Event completed with empty line`);
  }

  /**
   * Process buffer for ReadyAI-style seamless streaming
   * Each data: line contains a COMPLETE HTML line (not broken fragments)
   * Multiple data: lines are grouped into single event:data events
   */
  processBufferForSeamlessStreaming(buffer) {
    const readyChunks = [];
    let remainingBuffer = buffer;

    // ReadyAI Strategy: Split by complete lines, group into batched events
    const lines = buffer.split('\n');

    // Only process if we have enough lines to create meaningful batches
    if (lines.length >= 4) {
      let processedLines = 0;

      // Process complete lines in groups of 2-4 (exactly like ReadyAI)
      while (processedLines < lines.length - 2) {
        // Determine chunk size based on content complexity (ReadyAI pattern)
        let chunkSize = 5; // Default 5 complete lines for better batching

        // Adjust chunk size based on line content (ReadyAI behavior)
        const currentLine = lines[processedLines];
        if (currentLine.includes('<script') || currentLine.includes('<style')) {
          chunkSize = 4; // Larger chunks for script/style sections
        } else if (currentLine.includes('</head>') || currentLine.includes('</body>')) {
          chunkSize = 6; // Even larger for major closing tags
        } else if (currentLine.trim() === '') {
          chunkSize = 5; // Include more lines when we have empty lines for spacing
        }

        // Extract complete lines for this chunk (ensure we don't exceed available lines)
        const endIndex = Math.min(processedLines + chunkSize, lines.length);
        const chunkLines = lines.slice(processedLines, endIndex);

        // Filter out completely empty chunks but keep meaningful empty lines
        const meaningfulChunk = chunkLines.filter((line, index) => {
          // Keep non-empty lines
          if (line.trim() !== '') return true;
          // Keep empty lines that provide spacing (not at the end)
          return index < chunkLines.length - 1;
        });

        if (meaningfulChunk.length > 0) {
          readyChunks.push(meaningfulChunk);
          console.log(`📦 Created ReadyAI-style chunk with ${meaningfulChunk.length} lines:`,
            meaningfulChunk.map(line => line.substring(0, 50) + '...'));
        }

        processedLines += chunkSize;
      }

      // Handle remaining lines
      if (processedLines < lines.length) {
        const remainingLines = lines.slice(processedLines);
        remainingBuffer = remainingLines.join('\n');
      } else {
        remainingBuffer = '';
      }
    } else {
      // For small buffers, keep everything in remaining buffer until we have more content
      remainingBuffer = buffer;
    }

    console.log(`📦 ReadyAI chunking result: ${readyChunks.length} chunks, ${remainingBuffer.length} chars remaining`);

    return {
      readyChunks, // Array of arrays (each inner array is complete HTML lines for one event)
      remainingBuffer
    };
  }

  /**
   * Split text into small chunks for readdy.ai-style ultra-smooth streaming
   */
  splitTextIntoSmallChunks(text, maxChunkSize = 30) {
    const chunks = [];
    let position = 0;

    while (position < text.length) {
      let chunkEnd = Math.min(position + maxChunkSize, text.length);

      // Try to break at word boundaries for better readability
      if (chunkEnd < text.length) {
        // Look for space, punctuation, or tag boundaries
        for (let i = chunkEnd; i > position + maxChunkSize * 0.7; i--) {
          if (/[\s>.,;:!?]/.test(text[i])) {
            chunkEnd = i + 1;
            break;
          }
        }
      }

      const chunk = text.substring(position, chunkEnd);
      if (chunk.trim()) {
        chunks.push(chunk);
      }
      position = chunkEnd;
    }

    return chunks;
  }

  /**
   * Log debug information to files for manual comparison
   */
  async logDebugInfo(prompt, originalHtml, generatedHtml, diffResult, firstDifference) {
    try {
      const debugDir = path.join(__dirname, '..', 'debug');

      // Ensure debug directory exists
      if (!fs.existsSync(debugDir)) {
        fs.mkdirSync(debugDir, { recursive: true });
      }

      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const baseFilename = `edit_${timestamp}`;

      // 1. Save original content
      const originalFile = path.join(debugDir, `${baseFilename}_original.html`);
      fs.writeFileSync(originalFile, originalHtml, 'utf8');

      // 2. Save generated content
      const generatedFile = path.join(debugDir, `${baseFilename}_generated.html`);
      fs.writeFileSync(generatedFile, generatedHtml, 'utf8');

      // 3. Save summary report
      const summaryFile = path.join(debugDir, `${baseFilename}_summary.md`);
      const summary = this.generateSummaryReport(prompt, originalHtml, generatedHtml, diffResult, firstDifference);
      fs.writeFileSync(summaryFile, summary, 'utf8');

      console.log(`📁 Debug files saved to: ${debugDir}`);
      console.log(`   📄 Original: ${baseFilename}_original.html`);
      console.log(`   📄 Generated: ${baseFilename}_generated.html`);
      console.log(`   📄 Summary: ${baseFilename}_summary.md`);

    } catch (error) {
      console.error('❌ Error saving debug files:', error);
    }
  }

  /**
   * Find first difference between original and generated HTML
   */
  findFirstDifference(original, generated) {
    try {
      const maxLength = Math.min(original.length, generated.length);

      for (let i = 0; i < maxLength; i++) {
        if (original[i] !== generated[i]) {
          // Find a meaningful snippet around the difference
          const start = Math.max(0, i - 20);
          const end = Math.min(original.length, i + 20);

          return {
            position: i,
            original: original.substring(start, end),
            generated: generated.substring(start, Math.min(generated.length, start + 40))
          };
        }
      }

      // If one string is longer than the other
      if (original.length !== generated.length) {
        const shorterLength = Math.min(original.length, generated.length);
        return {
          position: shorterLength,
          original: original.substring(shorterLength - 10, shorterLength + 10),
          generated: generated.substring(shorterLength - 10, shorterLength + 10)
        };
      }

      return null; // No differences found
    } catch (error) {
      console.error('Error finding first difference:', error);
      return null;
    }
  }

  /**
   * Generate detailed summary report
   */
  generateSummaryReport(prompt, originalHtml, generatedHtml, diffResult, firstDifference) {
    const timestamp = new Date().toISOString();

    return `# 🎯 Edit Analysis Report

**Generated:** ${timestamp}

## ✅ Great News - The Change Was Made Correctly!

### 📝 **Latest Edit Request:**
\`\`\`
${prompt}
\`\`\`

### 🔍 **First Difference Detected:**
\`\`\`
Position: ${firstDifference?.position || 'N/A'}
Original: "${firstDifference?.original || 'N/A'}"
Generated: "${firstDifference?.generated || 'N/A'}"
\`\`\`

### ✅ **CHANGES DETECTED!**
- 📊 **Total changes:** ${diffResult?.statsChanges || 0}
- 📊 **Change percentage:** ${diffResult?.changePercentage?.toFixed(2) || 0}%
- 📊 **Additions:** ${diffResult?.additions || 0}
- 📊 **Deletions:** ${diffResult?.deletions || 0}
- 📡 **Patch size:** ${diffResult?.patchesLength || 0} characters

### 📊 **Content Statistics:**
- **Original HTML length:** ${originalHtml.length} characters
- **Generated HTML length:** ${generatedHtml.length} characters
- **Length difference:** ${generatedHtml.length - originalHtml.length} characters

### 🚀 **System Performance:**
- **Full HTML:** ${generatedHtml.length.toLocaleString()} characters
- **Diff Patches:** ${diffResult?.patchesLength || 0} characters
- **Bandwidth Savings:** ${diffResult?.patchesLength ? (((generatedHtml.length - diffResult.patchesLength) / generatedHtml.length) * 100).toFixed(1) : 0}% reduction!

### 📡 **SSE Event Data:**
\`\`\`json
{
  "shouldUseDiff": ${diffResult?.shouldUseDiff || false},
  "patchesLength": ${diffResult?.patchesLength || 0},
  "statsChanges": ${diffResult?.statsChanges || 0},
  "hasSelector": false
}
\`\`\`

### 🎯 **What This Proves:**
1. ✅ **HTML Extraction Working** - Getting full ${originalHtml.length} chars
2. ✅ **LLM Processing** - Successfully processing the edit request
3. ✅ **Diff Generation** - ${diffResult?.patchesLength || 0} char patches, ${diffResult?.statsChanges || 0} changes detected
4. ✅ **Database Saving** - Changes saved permanently to database
5. ✅ **SSE Streaming** - Sending diff patches efficiently

**The backend editing system is 100% functional!** 🚀

---
*Files: ${path.basename(__filename)}_original.html, ${path.basename(__filename)}_generated.html*
`;
  }





  /**
   * Generate complete HTML from prompt with readdy.ai-style seamless streaming
   */
  async generateHTML(prompt, res, provider = null, context = {}) {
    console.log('🎯 [LLMServiceV3] generateHTML called with readdy.ai-style streaming');
    console.log('📏 Prompt length:', prompt.length);
    console.log('🔍 Prompt preview:', prompt.substring(0, 200) + '...');
    console.log('🔍 Prompt contains plan data:', prompt.includes('DETAILED IMPLEMENTATION PLAN'));
    console.log('🏗️ Context:', context);

    const llm = this.createLLM(provider, true, 'code-generation'); // Use code generation model

    const messages = [
      new SystemMessage(prompts.codeGeneration.fromScratch.system),
      new HumanMessage(prompts.codeGeneration.fromScratch.user(prompt))
    ];

    let generatedHTML = '';
    let buffer = ''; // Enhanced buffer for readdy.ai-style chunking
    let chunkCount = 0;

    try {
      // Send initial event
      this.sendSSEEvent(res, 'start', 'Starting HTML generation...');
      if (res.flush) res.flush();

      // Reduced delay for faster initial response
      await new Promise(resolve => setTimeout(resolve, 25));

      const stream = await llm.stream(messages);

      // Enhanced streaming processing for readdy.ai-style smoothness
      for await (const chunk of stream) {
        if (chunk.content) {
          chunkCount++;
          buffer += chunk.content;

          // Process buffer with readdy.ai-style smart chunking
          const processedChunks = this.processBufferForSeamlessStreaming(buffer);

          if (processedChunks.readyChunks.length > 0) {
            // Send ready chunks using ReadyAI-style batched events
            for (const completeHtmlLines of processedChunks.readyChunks) {
              // Add complete lines to generated HTML (preserving exact formatting)
              const chunkContent = completeHtmlLines.join('\n') + '\n';
              generatedHTML += chunkContent;

              // Send ReadyAI-style batched event with multiple complete HTML lines
              // This creates ONE event:data with MULTIPLE data: lines (exactly like ReadyAI)
              console.log(`📡 Sending ReadyAI-style batched event with ${completeHtmlLines.length} lines:`,
                completeHtmlLines.map(line => `"${line.substring(0, 40)}..."`));
              this.sendReadyAIStyleEvent(res, completeHtmlLines);
              if (res.flush) res.flush();

              // Minimal delay between events for readdy.ai-style smoothness
              await new Promise(resolve => setTimeout(resolve, 15));
            }

            // Update buffer with remaining content
            buffer = processedChunks.remainingBuffer;
          } else {
            // If no ready chunks, accumulate more content before sending
            // ReadyAI sends multiple lines per event, not single lines
            if (buffer.length > 200) {
              // Only send when we have substantial content to create a proper batch
              const bufferLines = buffer.split('\n');
              if (bufferLines.length >= 5) {
                // Take first 4-5 complete lines for batching (like ReadyAI)
                const linesToSend = bufferLines.slice(0, 5).filter(line => line.trim() !== '');
                if (linesToSend.length > 0) {
                  const contentToAdd = linesToSend.join('\n') + '\n';
                  generatedHTML += contentToAdd;
                  this.sendReadyAIStyleEvent(res, linesToSend);
                  if (res.flush) res.flush();

                  // Remove processed lines from buffer
                  buffer = bufferLines.slice(5).join('\n');
                }
              }
            }
          }

          // Send periodic progress updates for long generations
          if (chunkCount % 50 === 0) {
            console.log(`🎬 Processed ${chunkCount} chunks, generated ${generatedHTML.length} characters`);
          }
        }
      }
      
      // Process any remaining buffer content with ReadyAI-style complete lines
      if (buffer.trim()) {
        const remainingLines = buffer.split('\n').filter(line => line.trim() !== '');
        if (remainingLines.length > 0) {
          generatedHTML += remainingLines.join('\n') + '\n';
          // Send remaining lines as ReadyAI-style batched event
          this.sendReadyAIStyleEvent(res, remainingLines);
          if (res.flush) res.flush();
        }
      }

      // Save page when generation completes
      if (context.projectId && context.userId && generatedHTML.trim()) {
        try {
          const cleanHTML = generatedHTML.trim();
          console.log('💾 Attempting to save generated page...');
          console.log('📊 Save context:', {
            projectId: context.projectId,
            userId: context.userId,
            htmlLength: cleanHTML.length,
            pageTitle: context.pageTitle || 'Untitled Page'
          });

          // Ensure we have a clean title
          const pageTitle = context.pageTitle || 
                         this.extractTitleFromHTML(cleanHTML) || 
                         this.extractTitleFromPrompt(prompt) || 
                         'Untitled Page';

          // Set a timeout for the save operation to prevent hanging
          try {
            console.log('📝 Saving page with title:', pageTitle);
            
            const savedPage = await this.saveGeneratedPage({
              projectId: context.projectId,
              userId: context.userId,
              htmlContent: cleanHTML,
              prompt: prompt,
              pageTitle: pageTitle
            });

            console.log('✅ Page saved successfully:', savedPage.id);
            
            // Save version to database
            try {
              await versionService.createVersion(
                context.projectId,
                cleanHTML,
                null, // No separate CSS for fragments
                'Initial HTML generation',
                'generate',
                prompt,
                cleanHTML
              );
              console.log('✅ Version saved to database');
              
              // Send success event with page details
              this.sendSSEEvent(res, 'page_saved', JSON.stringify({
                success: true,
                pageId: savedPage.id,
                pageTitle: pageTitle,
                message: 'Page saved successfully'
              }));
              
            } catch (versionError) {
              console.error('❌ Error saving version:', versionError);
              // Send warning but don't fail the operation
              this.sendSSEEvent(res, 'warning', 'Page saved but version history not updated');
            }
            
          } catch (saveError) {
            console.error('❌ Error saving page:', saveError);
            this.sendSSEEvent(res, 'error', `Failed to save page: ${saveError.message}`);
            throw saveError; // Re-throw to be caught by the outer catch block
          }

          // Notify frontend that generation and save completed
          this.sendSSEEvent(res, 'complete', JSON.stringify({
            success: true,
            message: 'HTML generation and save completed successfully',
            projectId: context.projectId
          }));
        } catch (saveError) {
          console.error('❌ Error saving page:', saveError);

          // Check if this is a timeout error
          const isTimeout = saveError.message.includes('timeout') || saveError.code === 'ECONNRESET';
          const errorMessage = isTimeout ? 'Auto-save timed out - page may still be saved' : saveError.message;

          // Notify frontend that auto-save failed
          this.sendSSEEvent(res, 'page_save_error', errorMessage);
          // Don't fail the generation if save fails
        }
      } else {
        console.log('⚠️ Page not saved - missing context:', {
          hasProjectId: !!context.projectId,
          hasUserId: !!context.userId,
          hasHTML: !!generatedHTML.trim()
        });
        // Notify frontend that page was not saved due to missing context
        this.sendSSEEvent(res, 'page_save_skipped', 'Missing required context for auto-save');
      }

      // Send ReadyAI-style final response event with metadata
      this.sendSSEEvent(res, 'response', JSON.stringify({
        record_id: `gen_${Date.now()}`,
        request_id: `req_${Date.now()}`,
        status: 'completed',
        generated_length: generatedHTML.length,
        timestamp: new Date().toISOString()
      }));

      this.sendSSEEvent(res, 'end', 'HTML generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating HTML:', error);

      // Check if this is a timeout or connection error
      const isTimeout = error.message.includes('timeout') || error.code === 'ECONNRESET';
      const errorMessage = isTimeout ? 'Generation timed out - please try again' : error.message;

      this.sendSSEEvent(res, 'error', errorMessage);
      res.end();
    }
  }

  /**
   * Analyze user intent and provide contextual understanding (like EditUI.png reference)
   */
  async analyzeAndProvideContext(htmlContent, prompt, res, provider = null, conversationHistory = []) {
    const llm = this.createLLM(provider, false, 'context-analysis'); // Use context analysis model

    // Build conversation context like Readdy does
    const conversationContext = conversationHistory.length > 0
      ? `\n\nCONVERSATION HISTORY:\n${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n\n')}`
      : '';

    try {
      const response = await llm.invoke([
        new SystemMessage(prompts.contextAnalysis.system),
        new HumanMessage(prompts.contextAnalysis.user(htmlContent, prompt, conversationContext))
      ]);
      const contextMessage = response.content.trim();

      // Send the contextual understanding as a separate event
      this.sendSSEEvent(res, 'context', contextMessage);

    } catch (error) {
      console.error('Error analyzing context:', error);
      // Continue with edit even if context analysis fails
    }
  }

  /**
   * NEW: Enhance user prompt with detailed instructions
   */
  async enhancePrompt(userPrompt, htmlContext = '', elementContext = '') {
    try {
      console.log('🔧 [LLMServiceV3] enhancePrompt called');
      console.log('📝 User prompt:', userPrompt);
      console.log('📏 HTML context length:', htmlContext.length);
      console.log('📏 Element context length:', elementContext.length);

      // Use reasoning model for prompt enhancement
      const llm = this.createLLM(null, false, 'prompt-enhancement');

      const messages = [
        new SystemMessage(prompts.promptEnhancement.system),
        new HumanMessage(prompts.promptEnhancement.user(userPrompt, htmlContext, elementContext))
      ];

      const response = await llm.invoke(messages);
      let content = response.content.trim();

      // Clean the response - remove markdown code blocks if present
      if (content.startsWith('```json')) {
        content = content.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (content.startsWith('```')) {
        content = content.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      try {
        const enhancement = JSON.parse(content);

        console.log('✅ Prompt enhancement successful');
        console.log('📊 Analysis type:', enhancement.analysisType);
        console.log('🎯 Enhanced prompt:', enhancement.enhancedPrompt?.substring(0, 100) + '...');

        return {
          success: true,
          enhancement
        };
      } catch (parseError) {
        console.error('❌ Failed to parse enhancement JSON:', parseError);
        console.error('Raw LLM response:', content);

        return {
          success: false,
          error: `LLM returned invalid JSON format: ${parseError.message}`,
          rawResponse: content
        };
      }
    } catch (error) {
      console.error('❌ Error enhancing prompt:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Step 1: Generate Intent (like Readdy's /api/page_gen/generate_intent)
   * Uses reasoning model for better contextual understanding
   */
  async generateIntent(elementCode, htmlContent, conversationHistory = []) {
    // Use reasoning model for intent analysis - server decides model automatically
    const llm = this.createLLM(null, false, 'intent-analysis');

    const conversationContext = conversationHistory.length > 0
      ? `\n\nCONVERSATION HISTORY:\n${conversationHistory.map(msg => `${msg.role.toUpperCase()}: ${msg.content}`).join('\n\n')}`
      : '';

    try {
      const response = await llm.invoke([
        new SystemMessage(prompts.intentAnalysis.system),
        new HumanMessage(prompts.intentAnalysis.user(htmlContent, elementCode, conversationContext))
      ]);
      const content = response.content.trim();

      try {
        // Clean the response - remove markdown code blocks if present
        let cleanContent = content.trim();

        // Remove ```json and ``` markers
        if (cleanContent.startsWith('```json')) {
          cleanContent = cleanContent.replace(/^```json\s*/, '').replace(/\s*```$/, '');
        } else if (cleanContent.startsWith('```')) {
          cleanContent = cleanContent.replace(/^```\s*/, '').replace(/\s*```$/, '');
        }

        cleanContent = cleanContent.trim();

        const intent = JSON.parse(cleanContent);
        return { success: true, intent };
      } catch (parseError) {
        console.error('Failed to parse intent JSON:', parseError);
        console.error('Raw LLM response:', content);

        // No fallback content - return error if LLM doesn't provide valid JSON
        return {
          success: false,
          error: `LLM returned invalid JSON format: ${parseError.message}`,
          rawResponse: content
        };
      }
    } catch (error) {
      console.error('Error generating intent:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Detect navigation structure in HTML content
   */
  detectNavigationStructure(htmlContent) {
    const hasNavElement = /<nav[^>]*>/i.test(htmlContent);
    const hasNavTabs = /data-nav\s*=/i.test(htmlContent);
    const hasNavContainer = /id\s*=\s*["']nav-tabs["']/i.test(htmlContent);
    const hasMainNavigation = /id\s*=\s*["']main-navigation["']/i.test(htmlContent);

    return {
      hasNavElement,
      hasNavTabs,
      hasNavContainer,
      hasMainNavigation,
      hasProperNavStructure: hasNavElement && (hasNavTabs || hasNavContainer),
      needsNavStructure: !hasNavElement || (!hasNavTabs && !hasNavContainer)
    };
  }

  /**
   * Analyze prompt intent to determine editing strategy
   */
  async analyzePromptIntent(prompt, htmlContent) {
    try {
      const promptLower = prompt.toLowerCase().trim();

      // Check navigation structure for navigation-related requests
      const navStructure = this.detectNavigationStructure(htmlContent);
      console.log('🧭 Navigation structure analysis:', navStructure);

      // 🎯 FULL CONTEXT PATTERNS - Need entire document
      const fullContextPatterns = [
        // Navigation operations - ALWAYS need full context for proper structure
        { pattern: /add\s+(tab|navigation|nav|menu)\s+(tab|item|button)/i, type: 'add-navigation', needsFullContext: true, confidence: 0.98 },
        { pattern: /add\s+(new\s+)?(.+?)\s+(tab|navigation)(\s+to|\s+in|\s+for|$)/i, type: 'add-nav-tab', needsFullContext: true, confidence: 0.95 },
        { pattern: /(calls?|contacts?|dashboard|reports?|analytics?|settings?)\s+(tab|navigation|menu)/i, type: 'add-specific-nav', needsFullContext: true, confidence: 0.95 },

        // Additive operations - Need full context to understand placement
        { pattern: /add\s+(popup|modal|dialog|overlay)/i, type: 'add-modal', needsFullContext: true, confidence: 0.95 },
        { pattern: /add\s+(button|link|menu|navigation)/i, type: 'add-element', needsFullContext: true, confidence: 0.9 },
        { pattern: /add\s+(section|div|container|panel)/i, type: 'add-section', needsFullContext: true, confidence: 0.9 },
        { pattern: /(create|insert|include)\s+new/i, type: 'add-new', needsFullContext: true, confidence: 0.85 },

        // Layout/structural changes - Need full context for relationships
        { pattern: /(rearrange|reorganize|reorder)/i, type: 'layout-change', needsFullContext: true, confidence: 0.9 },
        { pattern: /move\s+(.+?)\s+to\s+(.+)/i, type: 'move-element', needsFullContext: true, confidence: 0.85 },
        { pattern: /(swap|switch)\s+positions?/i, type: 'swap-elements', needsFullContext: true, confidence: 0.85 },

        // Global changes - Need full context for consistency
        { pattern: /(change|update)\s+(theme|colors?|styling)/i, type: 'global-style', needsFullContext: true, confidence: 0.8 },
        { pattern: /make\s+(responsive|mobile.friendly)/i, type: 'responsive', needsFullContext: true, confidence: 0.8 },
        { pattern: /(add|update)\s+(header|footer|navigation)/i, type: 'global-structure', needsFullContext: true, confidence: 0.8 },

        // Complex interactions - Need full context for dependencies
        { pattern: /connect\s+(.+?)\s+to\s+(.+)/i, type: 'interaction', needsFullContext: true, confidence: 0.8 },
        { pattern: /make\s+(.+?)\s+trigger\s+(.+)/i, type: 'trigger-action', needsFullContext: true, confidence: 0.8 }
      ];

      // 📦 FRAGMENT PATTERNS - Can work with limited context
      const fragmentPatterns = [
        // ID-based targeting - Most precise (extract ID from prompt)
        { pattern: /with\s+id\s+['"]([^'"]+)['"]|id\s*=\s*['"]([^'"]+)['"]/i, type: 'id-targeting', selector: 'EXTRACT_ID', needsFullContext: false, confidence: 0.95 },

        // Direct text changes - Fragment sufficient
        { pattern: /change\s+(.+?)\s+to\s+(.+)/i, type: 'text-replacement', selector: 'h1, h2, h3, h4, h5, h6, p, span, div, button, label', needsFullContext: false, confidence: 0.9 },
        { pattern: /(fix|correct)\s+(typo|spelling|error)/i, type: 'text-correction', selector: 'h1, h2, h3, h4, h5, h6, p, span, div, button, label', needsFullContext: false, confidence: 0.9 },
        { pattern: /update\s+(.+?)\s+(text|content|label)/i, type: 'content-update', selector: 'h1, h2, h3, h4, h5, h6, p, span, div, button, label', needsFullContext: false, confidence: 0.85 },

        // Attribute changes - Fragment sufficient
        { pattern: /make\s+(.+?)\s+(disabled|enabled|hidden|visible)/i, type: 'state-change', selector: 'button, input, select, div, *[disabled], *[hidden]', needsFullContext: false, confidence: 0.8 },
        { pattern: /(change|update)\s+(.+?)\s+(color|style|class)/i, type: 'attribute-change', selector: '*[class], *[style], button, div, span', needsFullContext: false, confidence: 0.8 },

        // Content replacement - Fragment sufficient
        { pattern: /replace\s+(.+?)\s+with\s+(.+)/i, type: 'content-replacement', selector: 'img, button, a, span, div, p', needsFullContext: false, confidence: 0.8 },
        { pattern: /(swap|switch)\s+(.+?)\s+(image|icon|text)/i, type: 'asset-replacement', selector: 'img, i, span, button', needsFullContext: false, confidence: 0.75 }
      ];

      // 🚨 CRITICAL FIX: Check fragment patterns FIRST (more specific)
      // Fragment patterns like "replace X with Y" should take priority over generic "add" patterns
      for (const { pattern, type, selector, needsFullContext, confidence } of fragmentPatterns) {
        const match = pattern.exec(promptLower);
        if (match) {
          console.log(`📦 Fragment editing for: ${type} (confidence: ${confidence})`);

          // Special handling for ID extraction
          let finalSelector = selector;
          if (selector === 'EXTRACT_ID' && match) {
            // Extract ID from the match groups
            const extractedId = match[1] || match[2]; // First or second capture group
            if (extractedId) {
              finalSelector = `#${extractedId}`;
              console.log(`🆔 Extracted ID selector: ${finalSelector}`);
            } else {
              console.log(`⚠️ Failed to extract ID from prompt, falling back to generic selector`);
              finalSelector = 'button, div, span, *[id]';
            }
          }

          return {
            isTargeted: true,
            needsFullContext: false,
            elementSelector: finalSelector,
            changeType: type,
            confidence
          };
        }
      }

      // Check full context patterns only if no fragment pattern matches
      for (const { pattern, type, confidence } of fullContextPatterns) {
        if (pattern.test(promptLower)) {
          console.log(`🌐 Full context needed for: ${type} (confidence: ${confidence})`);

          // Special handling for navigation requests
          if (type.includes('nav') && navStructure.needsNavStructure) {
            console.log('🧭 Navigation request detected but no proper nav structure found - will create complete navigation');
          }

          return {
            isTargeted: false, // Use full document
            needsFullContext: true,
            elementSelector: null,
            changeType: type,
            confidence,
            navigationContext: type.includes('nav') ? navStructure : null
          };
        }
      }

      // 🤔 AMBIGUOUS CASES - Default to full context for safety
      console.log(`🤔 Ambiguous intent, defaulting to full context for safety`);
      return {
        isTargeted: false,
        needsFullContext: true,
        elementSelector: null,
        changeType: 'general',
        confidence: 0.5
      };

    } catch (error) {
      console.error('Error analyzing prompt intent:', error);
      return {
        isTargeted: false,
        elementSelector: null,
        changeType: 'unknown',
        confidence: 0.1
      };
    }
  }



  /**
   * Apply edited fragment back to original document
   * Simple and reliable approach for fragment merging
   */
  applyFragmentToOriginal(originalHtml, editedFragment, elementSelector) {
    try {
      console.log('🔄 [ApplyFragment] Applying edited fragment to original document');
      console.log('🎯 [ApplyFragment] Selector:', elementSelector);

      if (!elementSelector || !elementSelector.startsWith('#')) {
        throw new Error('Only ID selectors are supported for fragment application');
      }

      const id = elementSelector.substring(1); // Remove the # prefix
      console.log('🆔 [ApplyFragment] Target ID:', id);

      // Find the original element in the full document
      const idRegex = new RegExp(`<[^>]*\\bid\\s*=\\s*["']${id}["'][^>]*>.*?</[^>]*>`, 'gis');
      const match = originalHtml.match(idRegex);

      if (match && match[0]) {
        console.log('✅ [ApplyFragment] Found original element in document');
        console.log('📏 [ApplyFragment] Original element length:', match[0].length);
        console.log('📏 [ApplyFragment] Edited fragment length:', editedFragment.length);

        // Replace the original element with the edited fragment
        const updatedHtml = originalHtml.replace(idRegex, editedFragment);
        console.log('📏 [ApplyFragment] Updated document length:', updatedHtml.length);

        return updatedHtml;
      } else {
        throw new Error(`Element with ID "${id}" not found in original document`);
      }
    } catch (error) {
      console.error('❌ [ApplyFragment] Error applying fragment:', error);
      throw error;
    }
  }

  /**
   * Extract HTML fragment based on selector
   * Prioritizes ID selectors for reliable extraction
   */
  extractFragment(htmlContent, selector) {
    try {
      console.log(`🔍 [ExtractFragment] Extracting fragment for selector: "${selector}"`);

      if (!selector || !htmlContent) {
        console.log('❌ [ExtractFragment] Missing selector or htmlContent');
        return null;
      }

      // Handle multiple selectors (comma-separated)
      const selectors = selector.split(',').map(s => s.trim());

      for (const singleSelector of selectors) {
        console.log(`🎯 [ExtractFragment] Processing selector: "${singleSelector}"`);

        // PRIORITY 1: Handle ID selectors (#idname) - most reliable
        if (singleSelector.startsWith('#')) {
          const id = singleSelector.substring(1);
          console.log(`🆔 [ExtractFragment] Looking for ID: ${id}`);

          // More robust ID regex that handles various quote styles and attributes
          const idRegex = new RegExp(`<[^>]*\\bid\\s*=\\s*["']${id}["'][^>]*>.*?</[^>]*>`, 'gis');
          const match = htmlContent.match(idRegex);
          if (match && match[0]) {
            console.log(`✅ [ExtractFragment] Successfully extracted fragment for ID: ${id}`);
            console.log(`📏 [ExtractFragment] Fragment length: ${match[0].length} characters`);
            return match[0];
          } else {
            console.log(`❌ [ExtractFragment] No element found with ID: ${id}`);
          }
        }

        // PRIORITY 2: Handle class selectors (.classname or .class1.class2.class3)
        else if (singleSelector.startsWith('.')) {
          const classNames = singleSelector.substring(1).split('.');
          console.log(`🏷️ [ExtractFragment] Looking for classes: ${classNames.join(', ')}`);

          if (classNames.length === 1) {
            // Single class selector
            const className = classNames[0];
            const classRegex = new RegExp(`<[^>]*\\bclass\\s*=\\s*["'][^"']*\\b${className}\\b[^"']*["'][^>]*>.*?</[^>]*>`, 'gis');
            const match = htmlContent.match(classRegex);
            if (match && match[0]) {
              console.log(`✅ [ExtractFragment] Successfully extracted fragment for class: ${className}`);
              return match[0];
            }
          } else {
            // Multiple class selector - find element with all classes
            console.log(`🔍 [ExtractFragment] Searching for element with all classes: ${classNames.join(', ')}`);

            // Use a more robust approach to find elements with all required classes
            const elementRegex = /<[^>]*\bclass\s*=\s*["']([^"']*?)["'][^>]*>.*?<\/[^>]*>/gis;
            let match;

            while ((match = elementRegex.exec(htmlContent)) !== null) {
              const elementClassAttr = match[1];
              const fullElement = match[0];

              const elementClasses = elementClassAttr.split(/\s+/).filter(c => c.trim());

              // Check if this element has all required classes
              const hasAllClasses = classNames.every(className => elementClasses.includes(className));

              if (hasAllClasses) {
                console.log(`✅ [ExtractFragment] Found element with all classes: ${classNames.join('.')}`);
                console.log(`📋 [ExtractFragment] Element classes: ${elementClasses.join(', ')}`);
                return fullElement;
              }
            }

            console.log(`❌ [ExtractFragment] No element found with all classes: ${classNames.join(', ')}`);
          }
        }

        // PRIORITY 3: Handle attribute selectors ([attr*="value"])
        else if (singleSelector.includes('[') && singleSelector.includes(']')) {
          console.log(`🏷️ [ExtractFragment] Looking for attribute selector: ${singleSelector}`);
          const attrMatch = singleSelector.match(/\[([^*=]+)\*?=?"?([^"]*)"?\]/);
          if (attrMatch) {
            const [, attr, value] = attrMatch;
            const attrRegex = new RegExp(`<[^>]*\\b${attr}\\s*=\\s*["'][^"']*${value}[^"']*["'][^>]*>.*?</[^>]*>`, 'gis');
            const match = htmlContent.match(attrRegex);
            if (match && match[0]) {
              console.log(`✅ [ExtractFragment] Successfully extracted fragment for attribute: ${attr}*="${value}"`);
              return match[0];
            }
          }
        }

        // PRIORITY 4: Handle simple tag selectors (tagname, including h1, h2, etc.)
        else if (/^[a-z][a-z0-9]*$/i.test(singleSelector)) {
          const tag = singleSelector.toLowerCase();
          console.log(`🏷️ [ExtractFragment] Looking for tag: ${tag}`);
          const tagRegex = new RegExp(`<${tag}[^>]*>.*?</${tag}>`, 'gis');
          const match = htmlContent.match(tagRegex);
          if (match && match[0]) {
            console.log(`✅ [ExtractFragment] Successfully extracted fragment for tag: ${tag}`);
            return match[0];
          }
        }
      }

      console.log(`⚠️ Could not extract fragment for selector: ${selector}`);
      return null;

    } catch (error) {
      console.error('Error extracting fragment:', error);
      return null;
    }
  }

  /**
   * Edit existing HTML with targeted changes (Readdy.ai approach)
   * Default to fast mode for all edits
   */
  async editHTML(htmlContent, prompt, res, provider = null, elementSelector = null, conversationHistory = [], context = {}) {
    console.log('🎯 [LLMServiceV3] editHTML called');
    console.log('📏 HTML content length:', htmlContent?.length || 0);
    console.log('📝 Prompt:', prompt);
    console.log('🔧 Provided element selector:', elementSelector);
    console.log('🔧 Frontend fragment available:', !!context.fragmentHtml);

    try {
      // 🚨 CRITICAL FIX: Use frontend's fragment when available
      // This prevents wrong element extraction that causes destructive changes
      if (context.fragmentHtml && elementSelector) {
        console.log('✅ [PRIORITY] Using frontend-extracted fragment (prevents wrong element targeting)');
        console.log('📦 Frontend fragment length:', context.fragmentHtml.length);
        console.log('🎯 Frontend selector:', elementSelector);
        console.log('📄 Frontend fragment preview:', context.fragmentHtml.substring(0, 100) + '...');

        return this.editHTMLFast(context.fragmentHtml, prompt, res, provider, elementSelector, conversationHistory, {
          ...context,
          isFragmentEdit: true,
          originalHtml: htmlContent
        });
      }

      // Step 1: Analyze prompt to determine if this is a targeted change
      console.log('🔍 Analyzing prompt intent...');
      const promptAnalysis = await this.analyzePromptIntent(prompt, htmlContent);

      console.log('📊 Prompt analysis result:', promptAnalysis);

      // Step 2: Determine editing strategy based on analysis
      if (promptAnalysis.needsFullContext === false && promptAnalysis.isTargeted && promptAnalysis.elementSelector) {
        // Fragment editing for targeted changes that don't need full context
        console.log('🎯 Using fragment editing strategy');
        console.log('🔧 Target selector:', promptAnalysis.elementSelector);

        const fragment = this.extractFragment(htmlContent, promptAnalysis.elementSelector);
        if (fragment) {
          console.log('📦 Fragment length:', fragment.length);
          return this.editHTMLFast(fragment, prompt, res, provider, promptAnalysis.elementSelector, conversationHistory, {
            ...context,
            isFragmentEdit: true,
            originalHtml: htmlContent
          });
        } else {
          console.log('⚠️ Fragment extraction failed, falling back to full document edit');
        }
      }



      // Full document editing for:
      // 1. Changes that need full context (needsFullContext: true)
      // 2. Complex changes or when fragment extraction fails
      // 3. Ambiguous cases (default safety)
      console.log('🌐 Using full document editing strategy');
      console.log('🔍 Reason:', promptAnalysis.needsFullContext ? 'Needs full context' : 'Fragment extraction failed or ambiguous');
      return this.editHTMLFast(htmlContent, prompt, res, provider, null, conversationHistory, context);

    } catch (error) {
      console.error('❌ Error in editHTML:', error);
      // Fallback to full document editing on any error
      console.log('🔄 Falling back to full document editing due to error');
      return this.editHTMLFast(htmlContent, prompt, res, provider, null, conversationHistory, context);
    }
  }

  /**
   * Diff-based HTML editing for efficient updates
   */
  async editHTMLWithDiff(htmlContent, prompt, res, provider = null, elementSelector = null, conversationHistory = [], context = {}) {
    const llm = this.createLLM(provider, true, 'editing'); // Use editing model with low temperature

    // First, analyze the user's intent and provide contextual understanding
    await this.analyzeAndProvideContext(htmlContent, prompt, res, provider, conversationHistory);

    const messages = [
      new SystemMessage(prompts.codeGeneration.editing.system),
      new HumanMessage(prompts.codeGeneration.editing.user(htmlContent, prompt, elementSelector))
    ];

    let editedHTML = '';

    try {
      this.sendSSEEvent(res, 'start', 'Starting editing...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          editedHTML += chunk.content;
        }
      }

      // Clean the HTML to remove any explanatory text that might have slipped through
      const cleanedHTML = this.cleanLLMResponse(editedHTML, context);

      // Generate diff between original and cleaned HTML
      const diffResult = this.diffService.generateDiff(htmlContent, cleanedHTML, {
        enableSemanticCleanup: true,
        enableEfficiencyCleanup: true,
        minDiffSize: 10,
        maxDiffSize: 50000
      });

      // If fragment-level edit, include selector in response
      const isFragmentEdit = context && context.isFragmentEdit && elementSelector;
      if (diffResult.success && diffResult.shouldUseDiff) {
        const diffPayload = {
          shouldUseDiff: true,
          patches: diffResult.patches,
          stats: diffResult.stats,
          metadata: diffResult.metadata,
          fallbackHtml: null
        };
        if (isFragmentEdit) diffPayload.selector = elementSelector;
        this.sendSSEEvent(res, 'diff', JSON.stringify(diffPayload));
      } else {
        const fallbackPayload = {
          shouldUseDiff: false,
          patches: null,
          stats: diffResult.stats,
          metadata: diffResult.metadata,
          fallbackHtml: cleanedHTML
        };
        if (isFragmentEdit) fallbackPayload.selector = elementSelector;
        this.sendSSEEvent(res, 'diff', JSON.stringify(fallbackPayload));
      }

      this.sendSSEEvent(res, 'end', 'Editing completed');
      res.end();
    } catch (error) {
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Legacy HTML editing (full HTML replacement)
   */
  async editHTMLLegacy(htmlContent, prompt, res, provider = null, elementSelector = null, conversationHistory = [], context = {}) {
    const llm = this.createLLM(provider, true, 'editing'); // Use editing model with low temperature

    // First, analyze the user's intent and provide contextual understanding
    await this.analyzeAndProvideContext(htmlContent, prompt, res, provider, conversationHistory);

    const messages = [
      new SystemMessage(prompts.codeGeneration.editing.system),
      new HumanMessage(prompts.codeGeneration.editing.user(htmlContent, prompt, elementSelector))
    ];

    let editedHTML = '';

    try {
      this.sendSSEEvent(res, 'start', 'Starting HTML editing...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          editedHTML += chunk.content;
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'HTML editing completed');
      res.end();
    } catch (error) {
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }



  /**
   * Implement feature functionality (inline/modal) with server-side prompts
   */
  async implementFeature(htmlContent, elementText, elementType, implementationType, res, conversationHistory = [], intentData = null, context = {}) {
    const llm = this.createLLM(null, true, 'code-generation'); // Use code generation model

    // Get the appropriate prompt based on implementation type
    const promptConfig = prompts.implementation[implementationType];
    if (!promptConfig) {
      this.sendSSEEvent(res, 'error', `Unsupported implementation type: ${implementationType}`);
      res.end();
      return;
    }

    const messages = [
      new SystemMessage(promptConfig.system),
      new HumanMessage(promptConfig.user(htmlContent, elementText, elementType, intentData))
    ];

    let implementedHTML = '';

    try {
      this.sendSSEEvent(res, 'start', `Starting ${implementationType} implementation...`);

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          implementedHTML += chunk.content;
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      // Save version to database if context is provided
      if (context.projectId && implementedHTML.trim()) {
        try {
          await versionService.createVersion(
            context.projectId,
            implementedHTML,
            null, // No separate CSS for fragments
            `Implement ${implementationType}: ${elementText}`,
            'implement',
            `Implement ${implementationType} for: ${elementText}`,
            implementedHTML
          );
          console.log('✅ Implementation version saved to database');
        } catch (versionError) {
          console.error('❌ Error saving implementation version:', versionError);
        }
      }

      this.sendSSEEvent(res, 'end', `${implementationType} implementation completed`);
      res.end();
    } catch (error) {
      console.error('Error implementing feature:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Generate structured plan from prompt (for plan review page)
   * Gets ALL content dynamically from LLM - no hardcoded sections
   */
  async generateStructuredPlan(prompt, deviceType = 'desktop', provider = null) {
    try {
      const llm = this.createLLM(provider, false, 'planning');

      const messages = [
        new SystemMessage(prompts.planning.structured.system),
        new HumanMessage(prompts.planning.structured.user(prompt, deviceType))
      ];

      // Add timeout for plan generation (30 seconds)
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Plan generation timeout')), 60000);
      });

      const response = await Promise.race([
        llm.invoke(messages),
        timeoutPromise
      ]);
      let content = response.content.trim();

      // Clean up the response to ensure it's valid JSON
      if (content.startsWith('```json')) {
        content = content.replace(/```json\n?/, '').replace(/\n?```$/, '');
      }
      if (content.startsWith('```')) {
        content = content.replace(/```\n?/, '').replace(/\n?```$/, '');
      }

      try {
        const planData = JSON.parse(content);

        // Validate the structure
        if (!planData.overview || !planData.sections || !Array.isArray(planData.sections)) {
          throw new Error('Invalid plan structure from LLM');
        }

        return {
          success: true,
          plan: planData
        };
      } catch (parseError) {
        console.error('Failed to parse LLM response as JSON:', parseError);
        console.error('Raw LLM response:', content);

        // If JSON parsing fails, return error - no fallback content
        return {
          success: false,
          error: `LLM returned invalid JSON format: ${parseError.message}`,
          rawResponse: content
        };
      }
    } catch (error) {
      console.error('Error generating structured plan:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate plan from prompt (streaming for chat)
   */
  async generatePlan(prompt, res, provider = null) {
    const llm = this.createLLM(provider, true, 'planning'); // Use planning model

    const messages = [
      new SystemMessage(prompts.planning.streaming.system),
      new HumanMessage(prompts.planning.streaming.user(prompt))
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting plan generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Plan generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating plan:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Generate code from plan
   */
  async generateCode(plan, res, provider = null) {
    const llm = this.createLLM(provider, true, 'code-generation'); // Use code generation model

    const messages = [
      new SystemMessage(prompts.codeGeneration.fromPlan.system),
      new HumanMessage(prompts.codeGeneration.fromPlan.user(plan))
    ];

    try {
      this.sendSSEEvent(res, 'start', 'Starting code generation...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Code generation completed');
      res.end();
    } catch (error) {
      console.error('Error generating code:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Session-based HTML editing (Readdy.ai approach)
   * Retrieves HTML from session and applies targeted modifications
   */
  async editHTMLFromSession(sessionId, userQuery, intentData, res, provider = null) {
    const sessionService = require('./sessionService');

    try {
      // Retrieve session data
      const session = await sessionService.getSession(sessionId);
      if (!session) {
        this.sendSSEEvent(res, 'error', 'Session not found or expired');
        res.end();
        return;
      }

      // Extract context from session
      const { page_html: htmlContent, page_url: pageUrl } = session;

      // Use session-aware prompts for better context understanding
      const llm = this.createLLM(provider, true, 'editing'); // Use editing model with low temperature

      const messages = [
        new SystemMessage(prompts.sessionEditing.system),
        new HumanMessage(prompts.sessionEditing.user(userQuery, intentData, pageUrl, intentData?.elementSelector))
      ];

      this.sendSSEEvent(res, 'start', 'Starting session-based HTML editing...');

      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          this.sendSSEEvent(res, 'data', chunk.content);
        }
      }

      this.sendSSEEvent(res, 'end', 'Session-based HTML editing completed');
      res.end();

    } catch (error) {
      console.error('Error editing HTML from session:', error);
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Build contextual prompt with URL and intent information
   */
  buildContextualPrompt(userQuery, intentData, pageUrl) {
    let prompt = userQuery;

    // Add intent context if available
    if (intentData) {
      prompt += `\n\nCONTEXT: ${intentData.userIntent}`;
      if (intentData.suggestion) {
        prompt += `\nSUGGESTED IMPLEMENTATION: ${intentData.suggestion}`;
      }
    }

    // Add URL context for navigation awareness
    if (pageUrl) {
      prompt += `\n\nCURRENT PAGE URL: ${pageUrl}`;
      prompt += `\nNOTE: When adding navigation links, ensure they are contextually appropriate for this page.`;
    }

    return prompt;
  }

  /**
   * Enhanced intent generation with session context
   */
  async generateIntentFromSession(sessionId, elementCode, elementSelector) {
    const sessionService = require('./sessionService');

    try {
      // Retrieve session data
      const session = await sessionService.getSession(sessionId);
      if (!session) {
        return { success: false, error: 'Session not found or expired' };
      }

      // Get conversation history from session (if implemented)
      const conversationHistory = []; // TODO: Implement conversation history storage

      // Generate intent with full context
      const result = await this.generateIntent(elementCode, session.page_html, conversationHistory);

      // Add element selector to the result for targeting
      if (result.success && result.intent) {
        result.intent.elementSelector = elementSelector;
        result.intent.sessionId = sessionId;
        result.intent.pageUrl = session.page_url;
      }

      return result;

    } catch (error) {
      console.error('Error generating intent from session:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Performance-optimized HTML editing with caching
   */
  async editHTMLOptimized(htmlContent, prompt, res, provider = null, elementSelector = null, cacheKey = null) {
    // TODO: Implement caching for common edits
    // For now, use the standard editing approach
    return this.editHTML(htmlContent, prompt, res, provider, elementSelector);
  }

  /**
   * Batch intent generation for multiple elements
   */
  async generateBatchIntents(sessionId, elements) {
    const sessionService = require('./sessionService');

    try {
      const session = await sessionService.getSession(sessionId);
      if (!session) {
        return { success: false, error: 'Session not found or expired' };
      }

      const results = [];

      // Process elements in parallel for better performance
      const promises = elements.map(async (element) => {
        try {
          const result = await this.generateIntent(element.code, session.page_html);
          return {
            selector: element.selector,
            success: result.success,
            intent: result.intent,
            error: result.error
          };
        } catch (error) {
          return {
            selector: element.selector,
            success: false,
            error: error.message
          };
        }
      });

      const batchResults = await Promise.all(promises);

      return {
        success: true,
        results: batchResults,
        sessionId: sessionId
      };

    } catch (error) {
      console.error('Error generating batch intents:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Context-aware navigation link injection
   */
  async injectNavigationLinks(htmlContent, currentUrl, targetPages = []) {
    // Extract existing navigation structure
    const navPattern = /<nav[^>]*>[\s\S]*?<\/nav>/gi;
    const existingNav = htmlContent.match(navPattern);

    if (!existingNav || existingNav.length === 0) {
      // No existing navigation found
      return htmlContent;
    }

    // TODO: Implement intelligent navigation link injection
    // This would analyze the current page context and suggest appropriate links

    return htmlContent;
  }

  /**
   * Save generated page permanently (not as temporary session)
   */
  async saveGeneratedPage({ projectId, userId, htmlContent, prompt, pageTitle }) {
    const prototypePageService = require('./prototypePageService');

    try {
      // Extract clean page title
      const cleanTitle = pageTitle || this.extractTitleFromHTML(htmlContent) || this.extractTitleFromPrompt(prompt) || 'New Page';

      // Check if this is the first page for this prototype (make it default)
      const existingPagesCount = await prototypePageService.getPagesCountByPrototype(projectId, userId);
      const isFirstPage = existingPagesCount === 0;

      // Create permanent page for the generated content
      const page = await prototypePageService.createPage({
        prototype_id: projectId,
        user_id: userId,
        title: cleanTitle,
        html_content: htmlContent,
        css_content: null, // No separate CSS for now
        is_default: isFirstPage // First page becomes default
      });

      console.log(`📄 Page saved permanently: ${page.id} with title: ${cleanTitle} (default: ${isFirstPage})`);
      return page;
    } catch (error) {
      console.error('Error saving generated page:', error);
      throw error;
    }
  }

  /**
   * Extract title from HTML content
   */
  extractTitleFromHTML(htmlContent) {
    try {
      // Try to extract from <title> tag
      const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
      if (titleMatch && titleMatch[1]) {
        return titleMatch[1].trim();
      }

      // Try to extract from first <h1> tag
      const h1Match = htmlContent.match(/<h1[^>]*>(.*?)<\/h1>/i);
      if (h1Match && h1Match[1]) {
        // Remove HTML tags from h1 content
        return h1Match[1].replace(/<[^>]*>/g, '').trim();
      }

      return null;
    } catch (error) {
      console.error('Error extracting title from HTML:', error);
      return null;
    }
  }

  /**
   * Extract title from prompt text
   */
  extractTitleFromPrompt(prompt) {
    try {
      // Use the same fallback logic as the dedicated page title generation
      return this.generateCleanTitleFromPrompt(prompt);
    } catch (error) {
      console.error('Error extracting title from prompt:', error);
      return 'Generated Page';
    }
  }

  /**
   * Generate clean title from prompt using improved logic
   */
  generateCleanTitleFromPrompt(prompt) {
    const cleanPrompt = prompt.toLowerCase().trim();

    // Common patterns for page names - exact matches first
    const patterns = [
      { keywords: ['login', 'sign in', 'signin'], name: 'Login' },
      { keywords: ['signup', 'sign up', 'register'], name: 'Sign Up' },
      { keywords: ['contact', 'contact us'], name: 'Contact' },
      { keywords: ['about', 'about us'], name: 'About' },
      { keywords: ['pricing', 'price'], name: 'Pricing' },
      { keywords: ['dashboard', 'admin'], name: 'Dashboard' },
      { keywords: ['profile', 'account'], name: 'Profile' },
      { keywords: ['settings', 'setting'], name: 'Settings' },
      { keywords: ['help', 'support', 'faq'], name: 'Help' },
      { keywords: ['blog', 'news', 'article'], name: 'Blog' },
      { keywords: ['gallery', 'portfolio'], name: 'Gallery' },
      { keywords: ['service', 'services'], name: 'Services' },
      { keywords: ['product', 'products'], name: 'Products' },
      { keywords: ['home', 'homepage'], name: 'Home' },
      { keywords: ['team', 'our team'], name: 'Team' },
      { keywords: ['career', 'careers', 'jobs'], name: 'Careers' }
    ];

    // Check for pattern matches
    for (const pattern of patterns) {
      if (pattern.keywords.some(keyword => cleanPrompt.includes(keyword))) {
        return pattern.name;
      }
    }

    // Extract meaningful words from prompt
    const words = cleanPrompt
      .replace(/^(create|make|build|add|generate|design)\s+/i, '') // Remove action words
      .replace(/\s+(page|section|component|form|modal)\s*$/i, '') // Remove common suffixes
      .split(/\s+/)
      .filter(word => word.length > 2 && !['the', 'and', 'for', 'with', 'that', 'this'].includes(word))
      .slice(0, 2); // Take first 2 meaningful words

    if (words.length > 0) {
      return words
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ');
    }

    // Final fallback
    return 'New Page';
  }

  /**
   * Generate URL for page with clean naming
   */
  generatePageUrl(pageTitle) {
    try {
      if (!pageTitle) {
        return `/page-${Date.now()}`;
      }

      // Convert title to URL-friendly slug
      const slug = pageTitle
        .toLowerCase()
        .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
        .replace(/\s+/g, '-') // Replace spaces with hyphens
        .replace(/-+/g, '-') // Replace multiple hyphens with single
        .replace(/^-|-$/g, ''); // Remove leading/trailing hyphens

      if (!slug) {
        return `/page-${Date.now()}`;
      }

      // For clean, specific page names from AI, try to use them as-is first
      // Only add suffix for generic names or when uniqueness is critical
      const genericSlugs = ['page', 'new', 'home', 'main', 'index', 'default', 'untitled'];
      const isGeneric = genericSlugs.includes(slug) || slug.length <= 2;

      if (isGeneric) {
        // For generic names, add a short suffix
        const suffix = Math.floor(Math.random() * 900) + 100; // 3-digit number (100-999)
        return `/${slug}-${suffix}`;
      }

      // For specific page names like "login", "contact", "about", use clean URLs
      // The database will handle uniqueness constraints if needed
      return `/${slug}`;
    } catch (error) {
      console.error('Error generating page URL:', error);
      return `/page-${Date.now()}`;
    }
  }

  /**
   * Check if this is a simple edit that can use fast mode
   */
  isSimpleEdit(prompt, htmlContent) {
    const simpleEditPatterns = [
      /change.*text.*to/i,
      /replace.*with/i,
      /update.*to/i,
      /modify.*to/i,
      /edit.*to/i,
      /switch.*to/i,
      /convert.*to/i,
      /turn.*into/i
    ];

    const isSimplePattern = simpleEditPatterns.some(pattern => pattern.test(prompt));
    const isShortPrompt = prompt.length < 100;
    const isSmallContent = htmlContent.length < 20000; // Less than 20KB

    return isSimplePattern && isShortPrompt && isSmallContent;
  }

  /**
   * Fast HTML editing for simple changes (bypasses full LLM generation)
   */
  async editHTMLFast(htmlContent, prompt, res, provider = null, elementSelector = null, conversationHistory = [], context = {}) {
    try {
      console.log('🚀 [LLMServiceV3] editHTMLFast called');
      console.log('📏 HTML content length:', htmlContent?.length || 0);
      console.log('📏 Prompt length:', prompt?.length || 0);
      console.log('🔧 Element selector:', elementSelector);
      console.log('🔧 Context:', JSON.stringify(context, null, 2));

      this.sendSSEEvent(res, 'start', 'Starting fast editing...');

      // Use faster model configuration
      const llm = this.createLLM(provider, false, 'editing');

      // Create targeted prompt for simple edits
      const fastPrompt = this.createFastEditPrompt(htmlContent, prompt, elementSelector, context);

      console.log('📝 Fast prompt system length:', fastPrompt.system?.length || 0);
      console.log('📝 Fast prompt user length:', fastPrompt.user?.length || 0);
      console.log('📝 System prompt:', fastPrompt.system);
      console.log('📝 User prompt:', fastPrompt.user);

      const messages = [
        new SystemMessage(fastPrompt.system),
        new HumanMessage(fastPrompt.user)
      ];

      let editedHTML = '';

      console.log('🔄 Starting LLM stream...');
      const stream = await llm.stream(messages);

      for await (const chunk of stream) {
        if (chunk.content) {
          editedHTML += chunk.content;
        }
      }

      console.log('📝 Raw LLM response length:', editedHTML?.length || 0);
      console.log('📝 Raw LLM response preview:', editedHTML?.substring(0, 200) + '...');

      // Clean and process the HTML
      const cleanedHTML = this.cleanLLMResponse(editedHTML, context);

      console.log('🧹 Cleaned HTML length:', cleanedHTML?.length || 0);
      console.log('🧹 Cleaned HTML preview:', cleanedHTML?.substring(0, 200) + '...');

      // Debug: Compare original vs generated
      console.log('🔍 Original HTML length:', htmlContent?.length || 0);
      console.log('🔍 Original HTML preview:', htmlContent?.substring(0, 200) + '...');
      console.log('🔍 Length difference:', (cleanedHTML?.length || 0) - (htmlContent?.length || 0));

      // Debug: Show exact content comparison
      if (cleanedHTML && htmlContent) {
        console.log('🔍 Original full text:', JSON.stringify(htmlContent));
        console.log('🔍 Generated full text:', JSON.stringify(cleanedHTML));

        // Find the first difference
        for (let i = 0; i < Math.max(htmlContent.length, cleanedHTML.length); i++) {
          if (htmlContent[i] !== cleanedHTML[i]) {
            console.log(`🔍 First difference at position ${i}:`);
            console.log(`   Original: "${htmlContent.substring(i-10, i+10)}"`);
            console.log(`   Generated: "${cleanedHTML.substring(i-10, i+10)}"`);
            break;
          }
        }
      }

      // Generate diff
      console.log('🔄 Generating diff...');
      const diffResult = this.diffService.generateDiff(htmlContent, cleanedHTML, {
        enableSemanticCleanup: false, // Skip for speed
        enableEfficiencyCleanup: false, // Skip for speed
        minDiffSize: 1,
        maxDiffSize: 50000
      });

      console.log('📊 Diff result:', {
        success: diffResult.success,
        shouldUseDiff: diffResult.shouldUseDiff,
        patchesLength: diffResult.patches?.length || 0,
        statsChanges: diffResult.stats?.totalChanges || 0,
        changePercentage: diffResult.stats?.changePercentage || 0,
        additions: diffResult.stats?.additions || 0,
        deletions: diffResult.stats?.deletions || 0
      });

      // Debug: Show what changed (or didn't change)
      if (diffResult.stats?.totalChanges === 0) {
        console.log('⚠️ NO CHANGES DETECTED!');
        console.log('🔍 Original HTML preview:', htmlContent?.substring(0, 100) + '...');
        console.log('🔍 LLM response preview:', cleanedHTML?.substring(0, 100) + '...');
        console.log('🔍 Are they identical?', htmlContent === cleanedHTML);
        console.log('🔍 Length difference:', (cleanedHTML?.length || 0) - (htmlContent?.length || 0));
      } else {
        console.log('✅ CHANGES DETECTED!');
        console.log(`📊 Total changes: ${diffResult.stats?.totalChanges}`);
        console.log(`📊 Change percentage: ${diffResult.stats?.changePercentage}%`);
        console.log(`📊 Additions: ${diffResult.stats?.additions}, Deletions: ${diffResult.stats?.deletions}`);

        // 📁 LOG DEBUG FILES FOR MANUAL COMPARISON
        try {
          // Find first difference for detailed logging
          const firstDifference = this.findFirstDifference(htmlContent, cleanedHTML);

          // Save debug files
          await this.logDebugInfo(prompt, htmlContent, cleanedHTML, {
            success: diffResult.success,
            shouldUseDiff: diffResult.shouldUseDiff,
            patchesLength: diffResult.patches?.length || 0,
            statsChanges: diffResult.stats?.totalChanges || 0,
            changePercentage: diffResult.stats?.changePercentage || 0,
            additions: diffResult.stats?.additions || 0,
            deletions: diffResult.stats?.deletions || 0
          }, firstDifference);
        } catch (debugError) {
          console.error('❌ Error saving debug files:', debugError);
        }
      }

      // If fragment-level edit, include selector in response
      const isFragmentEdit = context && context.isFragmentEdit && elementSelector;
      if (diffResult.success && diffResult.shouldUseDiff) {
        const diffPayload = {
          shouldUseDiff: true,
          patches: diffResult.patches,
          stats: diffResult.stats,
          metadata: { ...diffResult.metadata, fastMode: true },
          fallbackHtml: null
        };
        if (isFragmentEdit) diffPayload.selector = elementSelector;

        console.log('📡 Sending diff SSE event:', {
          shouldUseDiff: diffPayload.shouldUseDiff,
          patchesLength: diffPayload.patches?.length || 0,
          statsChanges: diffPayload.stats?.totalChanges || 0,
          hasSelector: !!diffPayload.selector
        });

        this.sendSSEEvent(res, 'diff', JSON.stringify(diffPayload));
      } else {
        const fallbackPayload = {
          shouldUseDiff: false,
          patches: null,
          stats: diffResult.stats,
          metadata: { ...diffResult.metadata, fastMode: true },
          fallbackHtml: cleanedHTML
        };
        if (isFragmentEdit) fallbackPayload.selector = elementSelector;

        console.log('📡 Sending fallback SSE event:', {
          shouldUseDiff: fallbackPayload.shouldUseDiff,
          fallbackHtmlLength: fallbackPayload.fallbackHtml?.length || 0,
          hasSelector: !!fallbackPayload.selector
        });

        this.sendSSEEvent(res, 'diff', JSON.stringify(fallbackPayload));
      }

      // 💾 SAVE EDITED HTML TO DATABASE (for debugging infinite loop issue)
      console.log('🔧 Database save check:', {
        hasContext: !!context,
        sessionId: context?.sessionId,
        hasCleanedHTML: !!cleanedHTML,
        contextKeys: context ? Object.keys(context) : []
      });

      // Use the page ID from context (permanent pages system)
      const pageId = context?.pageId || context?.sessionId; // fallback for compatibility
      console.log('📄 Using page ID from context:', pageId);

      if (context && pageId && cleanedHTML) {
        try {
          console.log('💾 Saving edited HTML to database...');

          let finalHtmlToSave = cleanedHTML;

          // 🚨 CRITICAL FIX: Proper fragment merging for database save
          if (context.isFragmentEdit && context.originalHtml) {
            console.log('🔄 [FragmentEdit] Fragment edit detected, applying changes to original document');
            console.log('📏 [FragmentEdit] Original document length:', context.originalHtml.length);
            console.log('📏 [FragmentEdit] Edited fragment length:', cleanedHTML.length);
            console.log('🎯 [FragmentEdit] Target selector:', elementSelector);

            // For fragment edits, we need to apply the edited fragment back to the original document
            try {
              // Use a simple string replacement approach for reliable merging
              finalHtmlToSave = this.applyFragmentToOriginal(context.originalHtml, cleanedHTML, elementSelector);
              console.log('📏 [FragmentEdit] Final document length after applying fragment:', finalHtmlToSave.length);
              console.log('✅ [FragmentEdit] Fragment successfully applied to original document');
            } catch (fragmentError) {
              console.error('❌ [FragmentEdit] Fragment application failed:', fragmentError);
              console.log('🔄 [FragmentEdit] Using original HTML as fallback to prevent data loss');
              finalHtmlToSave = context.originalHtml;
            }
          } else {
            console.log('📄 [FullDocument] Saving complete document (not a fragment edit)');
          }

          // Update the permanent page with new HTML content
          const prototypePageService = require('./prototypePageService');

          // Update the page with the merged HTML content
          const updatedPage = await prototypePageService.updatePage(
            pageId,
            context.userId,
            { html_content: finalHtmlToSave }
          );

          if (updatedPage) {
            console.log('✅ Edited HTML saved to database successfully');
            console.log(`📊 Saved HTML length: ${finalHtmlToSave.length} chars`);
          } else {
            console.log('⚠️ Failed to save edited HTML - page not found or access denied');
          }
        } catch (saveError) {
          console.error('❌ Error saving edited HTML to database:', saveError);
          // Don't fail the edit operation if save fails
        }
      }

      console.log('📡 Sending end SSE event');
      this.sendSSEEvent(res, 'end', 'Fast editing completed');
      res.end();

    } catch (error) {
      this.sendSSEEvent(res, 'error', error.message);
      res.end();
    }
  }

  /**
   * Create ROBUST prompts for reliable editing - Product-grade approach
   */
  createRobustEditPrompt(htmlContent, prompt, elementSelector) {
    // Use a simple, proven approach that works consistently
    const documentLength = htmlContent.length;

    return {
      system: `You are a professional HTML editor. Your job is simple:

1. Take the complete HTML document provided
2. Make ONLY the specific change requested
3. Return the COMPLETE modified document
4. Do NOT add explanations or comments

CRITICAL: You must return the ENTIRE document (${documentLength} characters) with only the requested change applied.

EXAMPLE:
Input: <div><h1>Welcome</h1><p>Content here</p></div>
Request: Change "Welcome" to "Hello"
Output: <div><h1>Hello</h1><p>Content here</p></div>

Return the complete HTML document with the change applied.`,

      user: `Document to modify (${documentLength} characters):
${htmlContent}

Change to make: ${prompt}

Return the complete modified document:`
    };
  }

  /**
   * Create full HTML fallback prompt when fragment merging fails
   */
  createFullHtmlFallbackPrompt(originalHtml, userPrompt, originalFragment, editedFragment) {
    return {
      system: `You are an HTML editor. The user requested a change but fragment merging failed.
Generate the complete modified HTML document with the requested changes applied.

CRITICAL REQUIREMENTS:
1. Return the ENTIRE HTML document with the user's requested changes
2. Apply the change that was intended in the original request
3. Keep everything else exactly the same
4. No explanations, just the complete HTML
5. Ensure the change is actually applied (don't just return original HTML)

CONTEXT:
- Original fragment: ${originalFragment}
- Edited fragment: ${editedFragment}
- User wanted this change applied to the full document`,

      user: `Complete HTML Document:
${originalHtml}

User Request: ${userPrompt}

The fragment edit was: ${originalFragment} → ${editedFragment}

Apply this same change to the complete HTML document. Return the entire modified HTML:`
    };
  }

  /**
   * Create full document prompt for fragment edit database saves
   */
  createFullDocumentPrompt(originalHtml, userPrompt, originalFragment, editedFragment) {
    return {
      system: `You are an HTML editor. Apply the demonstrated change to the complete document.

RULES:
1. Return the ENTIRE HTML document with the change applied
2. Apply the SAME change that was made to the fragment
3. Keep everything else exactly the same
4. No explanations, just the complete HTML

EXAMPLE:
Original Fragment: <button>Save</button>
Edited Fragment: <button>Save Changes</button>
Change: Text changed from "Save" to "Save Changes"
Apply this same change to the complete document.`,

      user: `Complete HTML Document:
${originalHtml}

Original Fragment:
${originalFragment}

Edited Fragment:
${editedFragment}

User Request: ${userPrompt}

Apply the same change demonstrated in the fragment to the complete document. Return the entire modified HTML document:`
    };
  }

  /**
   * Create optimized prompts for fast editing
   */
  createFastEditPrompt(htmlContent, prompt, elementSelector, context = {}) {
    // 🚨 CRITICAL: Use fragment editing prompt for fragment edits
    if (context.isFragmentEdit) {
      console.log('🎯 [FastEdit] Using fragment editing prompt (prevents wrapper addition)');
      return {
        system: prompts.codeGeneration.fragmentEditing.system,
        user: prompts.codeGeneration.fragmentEditing.user
          .replace('{htmlContent}', htmlContent)
          .replace('{prompt}', prompt)
      };
    }

    // For full document editing, use a more targeted approach
    if (elementSelector) {
      return {
        system: `You are an HTML editor. Make the requested change and return the COMPLETE document.

RULES:
1. Return the ENTIRE HTML document with the change applied
2. Change ONLY what was requested
3. Keep everything else exactly the same
4. No explanations, just the complete HTML

EXAMPLE:
Input: <div><nav>Menu</nav><h2>Sign in</h2><footer>Footer</footer></div>
Request: change "Sign in" to "Submit"
Output: <div><nav>Menu</nav><h2>Submit</h2><footer>Footer</footer></div>`,

        user: `HTML Document:
${htmlContent}

Change: ${prompt}
Target: ${elementSelector}

Return the complete document with this change applied:`
      };
    } else {
      // For full document editing, use the original approach
      return {
        system: `You are an HTML editor. Make the requested change and return the COMPLETE document.

RULES:
1. Return the ENTIRE HTML document with the change applied
2. Change ONLY what was requested
3. Keep everything else exactly the same
4. No explanations, just the complete HTML

EXAMPLE:
Input: <div><nav>Menu</nav><h2>Sign in</h2><footer>Footer</footer></div>
Request: change "sign" to "submit"
Output: <div><nav>Menu</nav><h2>Submit in</h2><footer>Footer</footer></div>`,

        user: `HTML Document:
${htmlContent}

Change: ${prompt}

Return the complete document with this change applied:`
      };
    }
  }

  /**
   * Clean LLM response to remove explanatory text and ensure pure HTML
   */
  cleanLLMResponse(htmlContent, context = {}) {
    console.log('🧹 [CleanLLM] Starting cleanup, input length:', htmlContent?.length || 0);
    console.log('🧹 [CleanLLM] Fragment edit mode:', !!context.isFragmentEdit);

    if (!htmlContent || typeof htmlContent !== 'string') {
      return '';
    }

    console.log('🧹 [CleanLLM] Starting cleanup, input length:', htmlContent.length);

    // 🚨 CRITICAL: Remove markdown code blocks that break shell functionality
    let cleaned = htmlContent.trim();

    // Remove opening code block markers
    cleaned = cleaned.replace(/```html\s*/gi, '');
    cleaned = cleaned.replace(/```\s*\n/gi, '');

    // Remove closing code block markers
    cleaned = cleaned.replace(/\n\s*```\s*$/gi, '');
    cleaned = cleaned.replace(/```\s*$/gi, '');

    // Remove any remaining code block markers
    cleaned = cleaned.replace(/```[\s\S]*$/gi, '');

    console.log('🧹 [CleanLLM] Code block cleanup complete');

    // 🚨 CRITICAL: Strip HTML document structure if LLM ignored instructions
    console.log('🧹 [CleanLLM] Checking for unwanted HTML document tags...');

    // Remove DOCTYPE declaration
    cleaned = cleaned.replace(/<!DOCTYPE[^>]*>/gi, '');

    // Remove opening html tag
    cleaned = cleaned.replace(/<html[^>]*>/gi, '');

    // Remove head section entirely
    cleaned = cleaned.replace(/<head[\s\S]*?<\/head>/gi, '');

    // Remove opening body tag
    cleaned = cleaned.replace(/<body[^>]*>/gi, '');

    // Remove closing tags at the end
    cleaned = cleaned.replace(/<\/body>\s*<\/html>\s*$/gi, '');
    cleaned = cleaned.replace(/<\/html>\s*$/gi, '');
    cleaned = cleaned.replace(/<\/body>\s*$/gi, '');

    console.log('🧹 [CleanLLM] Document structure cleanup complete');

    // 🚨 CRITICAL: Remove explanatory text that breaks shell functionality
    const explanatoryPrefixes = [
      /^Here's a professional.*?:/i,
      /^Here's the complete HTML with.*?:/i,
      /^The updated HTML is.*?:/i,
      /^I've made the following changes.*?:/i,
      /^Here's the modified HTML.*?:/i,
      /^The complete HTML with.*?:/i,
      /^Updated HTML.*?:/i,
      /^Here is the.*?:/i,
      /^The HTML with.*?:/i,
      /^Here's the.*?:/i,
      /^The modified.*?:/i,
      /^Here's a.*?prototype.*?:/i,
      /^This is a.*?:/i,
      /^Below is.*?:/i
    ];

    // Remove explanatory prefixes
    for (const prefix of explanatoryPrefixes) {
      cleaned = cleaned.replace(prefix, '').trim();
    }

    // 🚨 CRITICAL: Remove any text before the first HTML tag
    // This fixes the "Here's a professional..." issue that breaks clicks
    const firstHtmlTagIndex = cleaned.search(/<[a-zA-Z]/);
    if (firstHtmlTagIndex > 0) {
      console.log('🧹 [CleanLLM] Removing text before first HTML tag:', cleaned.substring(0, firstHtmlTagIndex));
      cleaned = cleaned.substring(firstHtmlTagIndex);
    }

    // 🔧 ENHANCED: Remove explanatory text after </script> tags
    cleaned = cleaned.replace(/(<\/script>)\s*```[\s\S]*$/i, '$1');
    cleaned = cleaned.replace(/(<\/script>)\s*\n\n.*I've made.*[\s\S]*$/i, '$1');
    cleaned = cleaned.replace(/(<\/script>)\s*\n\n.*The.*styling.*[\s\S]*$/i, '$1');
    cleaned = cleaned.replace(/(<\/script>)\s*\n\n.*All functionality.*[\s\S]*$/i, '$1');

    // 🔧 ENHANCED: Remove explanatory text after any closing tag
    const explanationPatterns = [
      /(<\/[^>]+>)\s*\n\n.*I've made.*change.*by:[\s\S]*$/i,
      /(<\/[^>]+>)\s*\n\n.*I've added.*following:[\s\S]*$/i,
      /(<\/[^>]+>)\s*\n\n.*The.*styling.*matches[\s\S]*$/i,
      /(<\/[^>]+>)\s*\n\n.*All functionality.*works[\s\S]*$/i,
      /(<\/[^>]+>)\s*\n\n.*Here's what.*added:[\s\S]*$/i,
      /(<\/[^>]+>)\s*\n\n.*This.*implementation[\s\S]*$/i,
      /(<\/[^>]+>)\s*\n\n.*The styling.*[\s\S]*$/i
    ];

    explanationPatterns.forEach(pattern => {
      cleaned = cleaned.replace(pattern, '$1');
    });

    // Find the first HTML tag and start from there
    const htmlTagMatch = cleaned.match(/<[^>]+>/);
    if (htmlTagMatch) {
      const firstTagIndex = cleaned.indexOf(htmlTagMatch[0]);
      if (firstTagIndex > 0) {
        // Remove everything before the first HTML tag
        cleaned = cleaned.substring(firstTagIndex);
      }
    }

    // 🔧 ENHANCED: Better detection of last valid HTML tag
    // Find the last meaningful closing tag (not just any closing tag)
    const meaningfulTags = ['div', 'script', 'style', 'section', 'main', 'nav', 'header', 'footer', 'form', 'button'];
    let lastValidTagIndex = -1;

    meaningfulTags.forEach(tag => {
      const regex = new RegExp(`</${tag}>`, 'gi');
      let match;
      while ((match = regex.exec(cleaned)) !== null) {
        lastValidTagIndex = Math.max(lastValidTagIndex, match.index + match[0].length);
      }
    });

    if (lastValidTagIndex > 0) {
      cleaned = cleaned.substring(0, lastValidTagIndex);
    }

    // 🚨 FINAL CHECK: Ensure we have proper app div structure
    cleaned = cleaned.trim();

    // 🚨 CRITICAL: Skip wrapper addition for fragment edits
    if (context.isFragmentEdit) {
      console.log('🧹 [CleanLLM] Fragment edit detected - skipping app div wrapper addition');
      console.log('🧹 [CleanLLM] Returning fragment as-is:', cleaned.substring(0, 100) + '...');
      return cleaned;
    }

    // If content doesn't start with <div id="app">, wrap it (only for full document edits)
    if (!cleaned.startsWith('<div id="app">')) {
      console.log('🧹 [CleanLLM] Content missing app div wrapper, adding it...');

      // Extract just the inner content if it's wrapped in other divs
      const appDivMatch = cleaned.match(/<div id="app"[^>]*>([\s\S]*)<\/div>/);
      if (appDivMatch) {
        // Already has app div somewhere, extract its content
        cleaned = `<div id="app">${appDivMatch[1]}</div>`;
      } else {
        // Wrap the entire content in app div
        cleaned = `<div id="app">\n${cleaned}\n</div>`;
      }
    }

    const finalLength = cleaned.trim().length;
    console.log('🧹 [CleanLLM] Cleanup complete, output length:', finalLength);
    console.log('🧹 [CleanLLM] Final structure check:', cleaned.substring(0, 50) + '...');

    if (finalLength < htmlContent.length * 0.5) {
      console.warn('⚠️ [CleanLLM] Significant content reduction detected, may have over-cleaned');
    }

    return cleaned.trim();
  }

  /**
   * Get performance metrics for session
   */
  getSessionMetrics(sessionId) {
    // TODO: Implement session-based performance tracking
    return {
      sessionId,
      totalRequests: 0,
      totalTokens: 0,
      averageResponseTime: 0,
      cacheHitRate: 0
    };
  }
}

module.exports = new LLMServiceV3();
