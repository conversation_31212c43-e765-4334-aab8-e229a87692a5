/**
 * Version Selector Component
 * Displays version history and allows navigation between prototype versions
 */

import React, { useState, useEffect, useRef } from 'react';
import { versionService, VersionMetadata, VersionDetails } from '../../services/versionService';

interface VersionSelectorProps {
  prototypeId: number;
  currentVersionNumber?: number;
  onVersionSelect: (version: VersionDetails) => void;
  onVersionRestore: (version: VersionDetails) => void;
  className?: string;
}

export default function VersionSelector({
  prototypeId,
  currentVersionNumber,
  onVersionSelect,
  onVersionRestore,
  className = ''
}: VersionSelectorProps) {
  const [versions, setVersions] = useState<VersionMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<number | null>(currentVersionNumber || null);
  const [error, setError] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load version history
  useEffect(() => {
    if (prototypeId) {
      loadVersionHistory();
    }
  }, [prototypeId]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadVersionHistory = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const versionHistory = await versionService.getVersionHistory(prototypeId, 20, 0);
      setVersions(versionHistory);
      
      // Set current version if not already set
      if (!selectedVersion && versionHistory.length > 0) {
        setSelectedVersion(versionHistory[0].version_number);
      }
    } catch (err) {
      console.error('Error loading version history:', err);
      setError('Failed to load version history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVersionClick = async (versionNumber: number) => {
    try {
      setIsLoading(true);
      const versionDetails = await versionService.getVersion(prototypeId, versionNumber);
      
      if (versionDetails) {
        setSelectedVersion(versionNumber);
        onVersionSelect(versionDetails);
        setIsExpanded(false);
      }
    } catch (err) {
      console.error('Error loading version details:', err);
      setError('Failed to load version details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVersionRestore = async (versionNumber: number) => {
    try {
      setIsLoading(true);
      const versionDetails = await versionService.getVersion(prototypeId, versionNumber);
      
      if (versionDetails) {
        onVersionRestore(versionDetails);
        setIsExpanded(false);
      }
    } catch (err) {
      console.error('Error restoring version:', err);
      setError('Failed to restore version');
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentVersionInfo = () => {
    if (!selectedVersion) return null;
    return versions.find(v => v.version_number === selectedVersion);
  };

  const currentVersion = getCurrentVersionInfo();

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-md p-3 ${className}`}>
        <div className="text-red-600 text-sm">{error}</div>
        <button
          onClick={loadVersionHistory}
          className="text-red-700 hover:text-red-800 text-xs underline mt-1"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Clean, Minimal Version Indicator */}
      <div className="flex items-center justify-between bg-white border border-gray-200 rounded-md px-4 py-3">
        <div className="flex items-center space-x-3">
          {/* Version Badge */}
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
              <span className="text-blue-700 text-sm font-medium">
                V{currentVersion?.version_number || versions[0]?.version_number || '1'}
              </span>
            </div>
            <div className="flex flex-col">
              <span className="text-gray-900 text-sm font-medium">
                Version {currentVersion?.version_number || versions[0]?.version_number || '1'}
              </span>
              {currentVersion && (
                <span className="text-gray-500 text-xs">
                  {versionService.getOperationName(currentVersion.operation_type)} • {versionService.formatRelativeTime(currentVersion.created_at)}
                </span>
              )}
            </div>
          </div>
        </div>

        {/* Version Actions */}
        <div className="flex items-center space-x-2">
          {isLoading && (
            <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
          )}

          {versions.length > 1 && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              disabled={isLoading}
              className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              title="View version history"
            >
              <svg
                className={`h-4 w-4 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Clean Version History Dropdown */}
      {isExpanded && versions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-md shadow-lg z-50 max-h-80 overflow-y-auto">
          <div className="p-2">
            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide px-2 py-1 mb-2">
              Version History
            </div>
            {versions.map((version) => (
              <div
                key={version.id}
                className={`group rounded-md p-3 hover:bg-gray-50 transition-colors ${
                  selectedVersion === version.version_number ? 'bg-blue-50 border border-blue-200' : 'border border-transparent'
                }`}
              >
                <div className="flex items-center justify-between">
                  <button
                    onClick={() => handleVersionClick(version.version_number)}
                    className="flex-1 text-left"
                  >
                    <div className="flex items-center space-x-3">
                      {/* Version Badge */}
                      <div className={`w-6 h-6 rounded-md flex items-center justify-center text-xs font-medium ${
                        selectedVersion === version.version_number
                          ? 'bg-blue-100 text-blue-700'
                          : 'bg-gray-100 text-gray-600'
                      }`}>
                        V{version.version_number}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">
                            Version {version.version_number}
                          </span>
                          <span className="text-xs text-gray-500">
                            {versionService.getOperationIcon(version.operation_type)}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500 mt-0.5">
                          {versionService.getOperationName(version.operation_type)} • {versionService.formatRelativeTime(version.created_at)}
                        </div>
                        {version.change_description && (
                          <div className="text-xs text-gray-600 mt-1 truncate">
                            {version.change_description}
                          </div>
                        )}
                      </div>
                    </div>
                  </button>

                  {selectedVersion !== version.version_number && (
                    <button
                      onClick={() => handleVersionRestore(version.version_number)}
                      className="ml-3 px-3 py-1.5 text-xs bg-white border border-gray-200 text-gray-700 rounded-md hover:bg-gray-50 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-colors opacity-0 group-hover:opacity-100"
                      title="Restore this version"
                    >
                      Restore
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
