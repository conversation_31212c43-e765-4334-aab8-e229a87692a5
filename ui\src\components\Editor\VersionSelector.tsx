/**
 * Version Selector Component
 * Displays version history and allows navigation between prototype versions
 */

import React, { useState, useEffect, useRef } from 'react';
import { versionService, VersionMetadata, VersionDetails } from '../../services/versionService';

interface VersionSelectorProps {
  prototypeId: number;
  currentVersionNumber?: number;
  onVersionSelect: (version: VersionDetails) => void;
  onVersionRestore: (version: VersionDetails) => void;
  className?: string;
}

export default function VersionSelector({
  prototypeId,
  currentVersionNumber,
  onVersionSelect,
  onVersionRestore,
  className = ''
}: VersionSelectorProps) {
  const [versions, setVersions] = useState<VersionMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedVersion, setSelectedVersion] = useState<number | null>(currentVersionNumber || null);
  const [error, setError] = useState<string | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Load version history
  useEffect(() => {
    if (prototypeId) {
      loadVersionHistory();
    }
  }, [prototypeId]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsExpanded(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const loadVersionHistory = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const versionHistory = await versionService.getVersionHistory(prototypeId, 20, 0);
      setVersions(versionHistory);
      
      // Set current version if not already set
      if (!selectedVersion && versionHistory.length > 0) {
        setSelectedVersion(versionHistory[0].version_number);
      }
    } catch (err) {
      console.error('Error loading version history:', err);
      setError('Failed to load version history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVersionClick = async (versionNumber: number) => {
    try {
      setIsLoading(true);
      const versionDetails = await versionService.getVersion(prototypeId, versionNumber);
      
      if (versionDetails) {
        setSelectedVersion(versionNumber);
        onVersionSelect(versionDetails);
        setIsExpanded(false);
      }
    } catch (err) {
      console.error('Error loading version details:', err);
      setError('Failed to load version details');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVersionRestore = async (versionNumber: number) => {
    try {
      setIsLoading(true);
      const versionDetails = await versionService.getVersion(prototypeId, versionNumber);
      
      if (versionDetails) {
        onVersionRestore(versionDetails);
        setIsExpanded(false);
      }
    } catch (err) {
      console.error('Error restoring version:', err);
      setError('Failed to restore version');
    } finally {
      setIsLoading(false);
    }
  };

  const getCurrentVersionInfo = () => {
    if (!selectedVersion) return null;
    return versions.find(v => v.version_number === selectedVersion);
  };

  const currentVersion = getCurrentVersionInfo();

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-3 ${className}`}>
        <div className="text-red-600 text-sm">{error}</div>
        <button
          onClick={loadVersionHistory}
          className="text-red-700 hover:text-red-800 text-xs underline mt-1"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Version Selector Button */}
      <button
        onClick={() => setIsExpanded(!isExpanded)}
        disabled={isLoading || versions.length === 0}
        className="w-full bg-white border border-gray-200 rounded-lg px-3 py-2 text-left hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-gray-500 text-sm">Version:</span>
            {currentVersion ? (
              <div className="flex items-center space-x-2">
                <span className="font-medium">v{currentVersion.version_number}</span>
                <span className="text-xs text-gray-500">
                  {versionService.getOperationIcon(currentVersion.operation_type)}
                </span>
                <span className="text-xs text-gray-500">
                  {versionService.formatRelativeTime(currentVersion.created_at)}
                </span>
              </div>
            ) : (
              <span className="text-gray-400">No versions</span>
            )}
          </div>
          <div className="flex items-center space-x-1">
            {isLoading && (
              <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            )}
            <svg
              className={`h-4 w-4 text-gray-400 transition-transform ${isExpanded ? 'rotate-180' : ''}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </button>

      {/* Version Dropdown */}
      {isExpanded && versions.length > 0 && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto">
          {versions.map((version) => (
            <div
              key={version.id}
              className={`px-3 py-2 hover:bg-gray-50 border-b border-gray-100 last:border-b-0 ${
                selectedVersion === version.version_number ? 'bg-blue-50 border-blue-200' : ''
              }`}
            >
              <div className="flex items-center justify-between">
                <button
                  onClick={() => handleVersionClick(version.version_number)}
                  className="flex-1 text-left"
                >
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm">v{version.version_number}</span>
                    <span className="text-xs">
                      {versionService.getOperationIcon(version.operation_type)}
                    </span>
                    <span className="text-xs text-gray-500">
                      {versionService.getOperationName(version.operation_type)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {versionService.formatRelativeTime(version.created_at)}
                  </div>
                  {version.change_description && (
                    <div className="text-xs text-gray-600 mt-1 truncate">
                      {version.change_description}
                    </div>
                  )}
                </button>
                
                {selectedVersion !== version.version_number && (
                  <button
                    onClick={() => handleVersionRestore(version.version_number)}
                    className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 focus:outline-none focus:ring-1 focus:ring-blue-500"
                    title="Restore this version"
                  >
                    Restore
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
