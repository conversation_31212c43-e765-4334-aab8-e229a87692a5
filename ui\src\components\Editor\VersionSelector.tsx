/**
 * Version Navigation Component
 * Clean, minimal version selector with arrow navigation
 */

import React, { useState, useEffect } from 'react';
import { versionService, VersionMetadata, VersionDetails } from '../../services/versionService';

interface VersionSelectorProps {
  prototypeId: number;
  currentVersionNumber?: number;
  onVersionSelect: (version: VersionDetails) => void;
  onVersionRestore: (version: VersionDetails) => void;
  className?: string;
}

export default function VersionSelector({
  prototypeId,
  currentVersionNumber,
  onVersionSelect,
  onVersionRestore,
  className = ''
}: VersionSelectorProps) {
  const [versions, setVersions] = useState<VersionMetadata[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [error, setError] = useState<string | null>(null);

  // Load version history
  useEffect(() => {
    if (prototypeId) {
      loadVersionHistory();
    }
  }, [prototypeId]);

  // Update current index when currentVersionNumber changes
  useEffect(() => {
    if (currentVersionNumber && versions.length > 0) {
      const index = versions.findIndex(v => v.version_number === currentVersionNumber);
      if (index !== -1) {
        setCurrentIndex(index);
      }
    }
  }, [currentVersionNumber, versions]);

  const loadVersionHistory = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const versionHistory = await versionService.getVersionHistory(prototypeId, 20, 0);
      // Sort versions by version number (ascending for proper navigation)
      const sortedVersions = versionHistory.sort((a, b) => a.version_number - b.version_number);
      setVersions(sortedVersions);

      // Set current index based on currentVersionNumber or default to latest
      if (currentVersionNumber) {
        const index = sortedVersions.findIndex(v => v.version_number === currentVersionNumber);
        setCurrentIndex(index !== -1 ? index : sortedVersions.length - 1);
      } else if (sortedVersions.length > 0) {
        setCurrentIndex(sortedVersions.length - 1); // Default to latest version
      }
    } catch (err) {
      console.error('Error loading version history:', err);
      setError('Failed to load version history');
    } finally {
      setIsLoading(false);
    }
  };

  const handleVersionChange = async (newIndex: number) => {
    if (newIndex < 0 || newIndex >= versions.length) return;

    try {
      setIsLoading(true);
      const version = versions[newIndex];
      const versionDetails = await versionService.getVersion(prototypeId, version.version_number);

      if (versionDetails) {
        setCurrentIndex(newIndex);
        onVersionSelect(versionDetails);
      }
    } catch (err) {
      console.error('Error loading version details:', err);
      setError('Failed to load version details');
    } finally {
      setIsLoading(false);
    }
  };

  const goToPreviousVersion = () => {
    if (currentIndex > 0) {
      handleVersionChange(currentIndex - 1);
    }
  };

  const goToNextVersion = () => {
    if (currentIndex < versions.length - 1) {
      handleVersionChange(currentIndex + 1);
    }
  };

  const currentVersion = versions[currentIndex];
  const isFirstVersion = currentIndex === 0;
  const isLastVersion = currentIndex === versions.length - 1;

  if (error) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-3 ${className}`}>
        <div className="text-red-600 text-sm">{error}</div>
        <button
          onClick={loadVersionHistory}
          className="text-red-700 hover:text-red-800 text-xs underline mt-1"
        >
          Retry
        </button>
      </div>
    );
  }

  if (versions.length === 0 && !isLoading) {
    return null; // Don't render if no versions
  }

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      {/* Left Arrow Button */}
      <button
        onClick={goToPreviousVersion}
        disabled={isFirstVersion || isLoading}
        className={`
          w-8 h-8 rounded-lg flex items-center justify-center
          backdrop-blur-sm bg-white/80 border border-gray-200/50
          shadow-sm hover:shadow-md transition-all duration-200
          ${isFirstVersion || isLoading
            ? 'opacity-40 cursor-not-allowed'
            : 'hover:bg-white hover:border-gray-300 active:scale-95'
          }
        `}
        title="Previous version"
      >
        <svg
          className="w-4 h-4 text-gray-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      {/* Current Version Button */}
      <div className={`
        px-4 py-2 rounded-lg flex items-center justify-center min-w-[60px]
        backdrop-blur-sm bg-white/90 border border-gray-200/50
        shadow-sm hover:shadow-md transition-all duration-200
        ${isLoading ? 'opacity-70' : 'hover:bg-white hover:border-gray-300'}
      `}>
        {isLoading ? (
          <div className="animate-spin h-4 w-4 border-2 border-blue-500 border-t-transparent rounded-full"></div>
        ) : (
          <span className="text-gray-900 text-sm font-medium">
            V{currentVersion?.version_number || '1'}
          </span>
        )}
      </div>

      {/* Right Arrow Button */}
      <button
        onClick={goToNextVersion}
        disabled={isLastVersion || isLoading}
        className={`
          w-8 h-8 rounded-lg flex items-center justify-center
          backdrop-blur-sm bg-white/80 border border-gray-200/50
          shadow-sm hover:shadow-md transition-all duration-200
          ${isLastVersion || isLoading
            ? 'opacity-40 cursor-not-allowed'
            : 'hover:bg-white hover:border-gray-300 active:scale-95'
          }
        `}
        title="Next version"
      >
        <svg
          className="w-4 h-4 text-gray-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>

  );
}
