/**
 * Demo component to showcase the updated VersionSelector design
 * This can be used for testing and design verification
 */

import React from 'react';

export default function VersionSelectorDemo() {
  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-2xl font-bold text-gray-900 mb-8">Version Selector Design Demo</h1>
        
        {/* Demo Container matching the center panel */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          {/* Mock Editor Content */}
          <div className="p-6 border-b border-gray-100">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Code Editor</h2>
            <div className="bg-gray-900 rounded-md p-4 text-green-400 font-mono text-sm">
              <div>&lt;div className="container"&gt;</div>
              <div className="ml-4">&lt;h1&gt;Hello World&lt;/h1&gt;</div>
              <div>&lt;/div&gt;</div>
            </div>
          </div>
          
          {/* Version Selector at Bottom */}
          <div className="border-t border-gray-100 bg-gray-50/50 px-6 py-4">
            {/* Clean Version Indicator */}
            <div className="flex items-center justify-between bg-white border border-gray-200 rounded-md px-4 py-3">
              <div className="flex items-center space-x-3">
                {/* Version Badge */}
                <div className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-blue-100 rounded-md flex items-center justify-center">
                    <span className="text-blue-700 text-sm font-medium">V3</span>
                  </div>
                  <div className="flex flex-col">
                    <span className="text-gray-900 text-sm font-medium">Version 3</span>
                    <span className="text-gray-500 text-xs">Generated • 2 minutes ago</span>
                  </div>
                </div>
              </div>

              {/* Version Actions */}
              <div className="flex items-center space-x-2">
                <button
                  className="p-1.5 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors"
                  title="View version history"
                >
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Design Notes */}
        <div className="mt-8 p-6 bg-blue-50 rounded-lg border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-3">Design Features</h3>
          <ul className="space-y-2 text-blue-800 text-sm">
            <li>• Clean, minimal design matching the form aesthetic</li>
            <li>• Version badge with blue accent color</li>
            <li>• Subtle background and border styling</li>
            <li>• Positioned at bottom of center panel</li>
            <li>• Responsive hover states and transitions</li>
            <li>• Consistent spacing and typography</li>
          </ul>
        </div>

        {/* Version History Dropdown Demo */}
        <div className="mt-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Version History Dropdown</h3>
          <div className="relative">
            <div className="bg-white border border-gray-200 rounded-md shadow-lg max-h-80 overflow-y-auto">
              <div className="p-2">
                <div className="text-xs font-medium text-gray-500 uppercase tracking-wide px-2 py-1 mb-2">
                  Version History
                </div>
                
                {/* Version 3 - Current */}
                <div className="group rounded-md p-3 bg-blue-50 border border-blue-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-blue-100 rounded-md flex items-center justify-center text-xs font-medium text-blue-700">
                        V3
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">Version 3</span>
                          <span className="text-xs text-gray-500">🤖</span>
                        </div>
                        <div className="text-xs text-gray-500 mt-0.5">
                          Generated • 2 minutes ago
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          Added responsive navigation
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Version 2 */}
                <div className="group rounded-md p-3 hover:bg-gray-50 transition-colors border border-transparent">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-gray-100 rounded-md flex items-center justify-center text-xs font-medium text-gray-600">
                        V2
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">Version 2</span>
                          <span className="text-xs text-gray-500">✏️</span>
                        </div>
                        <div className="text-xs text-gray-500 mt-0.5">
                          Manual Edit • 1 hour ago
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          Updated styling and layout
                        </div>
                      </div>
                    </div>
                    <button className="ml-3 px-3 py-1.5 text-xs bg-white border border-gray-200 text-gray-700 rounded-md hover:bg-gray-50 hover:border-gray-300 transition-colors opacity-0 group-hover:opacity-100">
                      Restore
                    </button>
                  </div>
                </div>

                {/* Version 1 */}
                <div className="group rounded-md p-3 hover:bg-gray-50 transition-colors border border-transparent">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-6 h-6 bg-gray-100 rounded-md flex items-center justify-center text-xs font-medium text-gray-600">
                        V1
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-medium text-gray-900">Version 1</span>
                          <span className="text-xs text-gray-500">🤖</span>
                        </div>
                        <div className="text-xs text-gray-500 mt-0.5">
                          Generated • 2 days ago
                        </div>
                        <div className="text-xs text-gray-600 mt-1">
                          Initial version
                        </div>
                      </div>
                    </div>
                    <button className="ml-3 px-3 py-1.5 text-xs bg-white border border-gray-200 text-gray-700 rounded-md hover:bg-gray-50 hover:border-gray-300 transition-colors opacity-0 group-hover:opacity-100">
                      Restore
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
