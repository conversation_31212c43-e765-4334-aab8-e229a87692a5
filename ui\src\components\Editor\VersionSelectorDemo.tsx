/**
 * Demo component to showcase the new arrow-based VersionSelector design
 * This can be used for testing and design verification
 */

import React, { useState } from 'react';

// Standalone Version Navigation Component for Demo
function VersionNavigation({
  versions = ['V1', 'V2', 'V3', 'V4'],
  onVersionChange
}: {
  versions?: string[];
  onVersionChange?: (version: string) => void;
}) {
  const [currentIndex, setCurrentIndex] = useState(versions.length - 1); // Start with latest version

  const goToPrevious = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1;
      setCurrentIndex(newIndex);
      onVersionChange?.(versions[newIndex]);
    }
  };

  const goToNext = () => {
    if (currentIndex < versions.length - 1) {
      const newIndex = currentIndex + 1;
      setCurrentIndex(newIndex);
      onVersionChange?.(versions[newIndex]);
    }
  };

  const isFirstVersion = currentIndex === 0;
  const isLastVersion = currentIndex === versions.length - 1;
  const currentVersion = versions[currentIndex];

  return (
    <div className="flex items-center space-x-2">
      {/* Left Arrow Button */}
      <button
        onClick={goToPrevious}
        disabled={isFirstVersion}
        className={`
          w-8 h-8 rounded-lg flex items-center justify-center
          backdrop-blur-sm bg-white/80 border border-gray-200/50
          shadow-sm hover:shadow-md transition-all duration-200
          ${isFirstVersion
            ? 'opacity-40 cursor-not-allowed'
            : 'hover:bg-white hover:border-gray-300 active:scale-95'
          }
        `}
        title="Previous version"
      >
        <svg
          className="w-4 h-4 text-gray-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      {/* Current Version Button */}
      <div className="
        px-4 py-2 rounded-lg flex items-center justify-center min-w-[60px]
        backdrop-blur-sm bg-white/90 border border-gray-200/50
        shadow-sm hover:shadow-md transition-all duration-200
        hover:bg-white hover:border-gray-300
      ">
        <span className="text-gray-900 text-sm font-medium">
          {currentVersion}
        </span>
      </div>

      {/* Right Arrow Button */}
      <button
        onClick={goToNext}
        disabled={isLastVersion}
        className={`
          w-8 h-8 rounded-lg flex items-center justify-center
          backdrop-blur-sm bg-white/80 border border-gray-200/50
          shadow-sm hover:shadow-md transition-all duration-200
          ${isLastVersion
            ? 'opacity-40 cursor-not-allowed'
            : 'hover:bg-white hover:border-gray-300 active:scale-95'
          }
        `}
        title="Next version"
      >
        <svg
          className="w-4 h-4 text-gray-600"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>
  );
}

export default function VersionSelectorDemo() {
  const [selectedVersion, setSelectedVersion] = useState('V4');

  return (
    <div className="p-8 bg-gradient-to-br from-gray-50 to-gray-100 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Version Navigation Component</h1>

        {/* Interactive Demo */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {/* Live Demo */}
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
            <div className="p-6 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">Live Demo</h2>
              <div className="bg-gray-900 rounded-lg p-4 text-green-400 font-mono text-sm mb-4">
                <div>&lt;div className="app"&gt;</div>
                <div className="ml-4">&lt;h1&gt;{selectedVersion} Content&lt;/h1&gt;</div>
                <div>&lt;/div&gt;</div>
              </div>
              <div className="text-sm text-gray-600">
                Current Version: <span className="font-medium text-gray-900">{selectedVersion}</span>
              </div>
            </div>

            {/* Version Navigation positioned at bottom-left */}
            <div className="relative h-20 bg-gradient-to-br from-gray-50 to-gray-100">
              <div className="absolute bottom-4 left-4">
                <VersionNavigation
                  onVersionChange={setSelectedVersion}
                />
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="space-y-6">
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">✨ Design Features</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Glassmorphism effect with backdrop blur</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Soft shadows and hover animations</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Disabled states for boundary versions</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Bottom-left positioning</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-1">•</span>
                  <span>Smooth transitions and active states</span>
                </li>
              </ul>
            </div>

            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">⚡ Behavior</h3>
              <ul className="space-y-3 text-gray-700">
                <li className="flex items-start space-x-2">
                  <span className="text-green-500 mt-1">•</span>
                  <span>Arrow navigation between versions</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-green-500 mt-1">•</span>
                  <span>Auto-disable at boundaries</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-green-500 mt-1">•</span>
                  <span>Callback on version change</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-green-500 mt-1">•</span>
                  <span>Loading states support</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Code Example */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
            <h3 className="text-lg font-semibold text-gray-900">Code Example</h3>
          </div>
          <div className="p-6">
            <pre className="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto text-sm">
{`<VersionNavigation
  versions={['V1', 'V2', 'V3', 'V4']}
  onVersionChange={(version) => {
    console.log('Version changed to:', version);
  }}
/>`}
            </pre>
          </div>
        </div>

        {/* Different Variations */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Single Version</h4>
            <div className="flex justify-center">
              <VersionNavigation versions={['V1']} />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Two Versions</h4>
            <div className="flex justify-center">
              <VersionNavigation versions={['V1', 'V2']} />
            </div>
          </div>

          <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <h4 className="font-semibold text-gray-900 mb-4">Many Versions</h4>
            <div className="flex justify-center">
              <VersionNavigation versions={['V1', 'V2', 'V3', 'V4', 'V5', 'V6']} />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
