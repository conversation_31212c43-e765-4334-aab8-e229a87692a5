/**
 * Hook for automatic prototype versioning integration
 * Monitors editor state and creates versions based on specific rules
 */

import { useEffect, useRef, useCallback } from 'react';
import { prototypeVersioningService } from '../services/prototypeVersioningService';

interface UsePrototypeVersioningProps {
  prototypeId: number | null;
  htmlContent: string | null;
  isGenerating: boolean;
  currentPageId: string | null;
  pages: any[];
  isVersionSwitching?: boolean;
  onVersionCreated?: (versionId: number) => void;
}

interface VersioningContext {
  lastHtmlContent: string | null;
  lastPageId: string | null;
  lastPageCount: number;
  hasInitialVersion: boolean;
  isFirstContentSet: boolean;
  isVersionSwitching: boolean;
  lastVersionSwitchTime: number;
}

export function usePrototypeVersioning({
  prototypeId,
  htmlContent,
  isGenerating,
  currentPageId,
  pages,
  isVersionSwitching = false,
  onVersionCreated
}: UsePrototypeVersioningProps) {
  const contextRef = useRef<VersioningContext>({
    lastHtmlContent: null,
    lastPageId: null,
    lastPageCount: 0,
    hasInitialVersion: false,
    isFirstContentSet: false,
    isVersionSwitching: false,
    lastVersionSwitchTime: 0
  });

  const lastGenerationStateRef = useRef<boolean>(false);

  /**
   * Handle version creation completion
   */
  const handleVersionCreated = useCallback((versionId: number) => {
    console.log('🎉 Version created callback:', versionId);
    onVersionCreated?.(versionId);
  }, [onVersionCreated]);

  /**
   * Check if this is the first page being created
   */
  const isFirstPage = useCallback((): boolean => {
    const context = contextRef.current;
    // Only consider it first page if:
    // 1. We have exactly 1 page
    // 2. We haven't created an initial version yet
    // 3. We have HTML content (page has been generated)
    // 4. This is the first time we're setting content (not a subsequent update)
    return pages.length === 1 &&
           !context.hasInitialVersion &&
           htmlContent &&
           !context.isFirstContentSet;
  }, [pages.length, htmlContent]);

  /**
   * Check if this is a content modification (not navigation)
   */
  const isContentModification = useCallback((): boolean => {
    const context = contextRef.current;

    // Don't consider it a modification if this is the first page being created
    if (pages.length === 1 && !context.hasInitialVersion) {
      return false;
    }

    // Don't consider it a modification if we're adding a new page
    if (pages.length > context.lastPageCount) {
      return false;
    }

    // CRITICAL: Don't consider it a modification if we're switching versions
    if (isVersionSwitching || context.isVersionSwitching) {
      console.log('⏭️ Skipping version creation: Version switching in progress');
      return false;
    }

    // Don't consider it a modification if version switch happened recently (within 2 seconds)
    const timeSinceLastSwitch = Date.now() - context.lastVersionSwitchTime;
    if (timeSinceLastSwitch < 2000) {
      console.log('⏭️ Skipping version creation: Recent version switch detected');
      return false;
    }

    // If page hasn't changed but content has, it's a modification
    if (context.lastPageId === currentPageId &&
        context.lastHtmlContent !== htmlContent &&
        htmlContent &&
        context.lastHtmlContent) {
      return true;
    }

    // If we're not generating and content changed, it's likely a manual edit
    if (!isGenerating &&
        context.lastHtmlContent !== htmlContent &&
        htmlContent &&
        context.lastHtmlContent &&
        context.hasInitialVersion) {
      return true;
    }

    return false;
  }, [currentPageId, htmlContent, isGenerating, pages.length, isVersionSwitching]);

  /**
   * Check if this is just page navigation
   */
  const isPageNavigation = useCallback((): boolean => {
    const context = contextRef.current;
    
    // Page changed but content is the same or we're just loading existing content
    return context.lastPageId !== currentPageId && context.lastHtmlContent === htmlContent;
  }, [currentPageId, htmlContent]);

  /**
   * Check if this is a new page addition
   */
  const isNewPageAddition = useCallback((): boolean => {
    const context = contextRef.current;
    // New page addition if:
    // 1. Page count increased
    // 2. We have more than 1 page total
    // 3. We already have an initial version (not the first page)
    return pages.length > context.lastPageCount &&
           pages.length > 1 &&
           context.hasInitialVersion;
  }, [pages.length]);

  /**
   * Create initial version for new prototype
   */
  const createInitialVersion = useCallback(async () => {
    if (!prototypeId || !htmlContent || contextRef.current.hasInitialVersion) {
      return;
    }

    console.log('🎯 Creating initial version for new prototype (readdy.ai style)');

    try {
      const versionId = await prototypeVersioningService.createInitialVersion(
        prototypeId,
        htmlContent
      );

      if (versionId) {
        contextRef.current.hasInitialVersion = true;
        handleVersionCreated(versionId);
        console.log('✅ Initial version V1 created successfully - version controls will now appear');

        // Emit event to trigger version UI to appear
        window.dispatchEvent(new CustomEvent('firstVersionCreated', {
          detail: { prototypeId, versionId }
        }));
      }
    } catch (error) {
      console.error('❌ Failed to create initial version:', error);
    }
  }, [prototypeId, htmlContent, handleVersionCreated]);

  /**
   * Create version for content modifications
   */
  const createModificationVersion = useCallback(async (immediate = false) => {
    if (!prototypeId || !htmlContent) {
      return;
    }

    const operationType = lastGenerationStateRef.current ? 'generation' : 'manual_edit';
    const changeDescription = lastGenerationStateRef.current 
      ? 'AI-generated content update'
      : 'Manual content modification';

    console.log(`🎯 Creating ${operationType} version for content modification`);

    try {
      await prototypeVersioningService.createContentModificationVersion(
        prototypeId,
        htmlContent,
        operationType,
        {
          changeDescription,
          immediate
        }
      );
    } catch (error) {
      console.error('❌ Failed to create modification version:', error);
    }
  }, [prototypeId, htmlContent]);

  /**
   * Main versioning logic
   */
  useEffect(() => {
    if (!prototypeId) {
      return;
    }

    // Don't run versioning logic while generating content
    if (isGenerating) {
      return;
    }

    // Add a small delay to ensure the editor state has stabilized
    const timer = setTimeout(() => {
      const context = contextRef.current;

      // Additional safety checks
      if (!htmlContent || htmlContent.trim().length === 0) {
        console.log('⏭️ Skipping versioning: No HTML content');
        return;
      }

      // Don't run if we're in the middle of a page transition
      if (context.lastPageId !== currentPageId && !htmlContent) {
        console.log('⏭️ Skipping versioning: Page transition without content');
        return;
      }
    
    // Determine versioning context
    const versioningContext = {
      isFirstPage: isFirstPage(),
      isContentModification: isContentModification(),
      isPageNavigation: isPageNavigation(),
      isNewPageAddition: isNewPageAddition(),
      isUIStateChange: false // We don't track UI state changes in this hook
    };

    console.log('🔍 Versioning context:', {
      ...versioningContext,
      prototypeId,
      currentPageId,
      htmlContentLength: htmlContent?.length || 0,
      pagesCount: pages.length,
      isGenerating,
      hasInitialVersion: context.hasInitialVersion
    });

    // Rule 1: Create initial version for first page
    if (versioningContext.isFirstPage && htmlContent && !context.isFirstContentSet) {
      console.log('📝 Rule 1: Creating initial version for first page');
      createInitialVersion();
      context.isFirstContentSet = true;
    }

    // Rule 2: Skip new page additions (without content modification)
    else if (versioningContext.isNewPageAddition && !versioningContext.isContentModification) {
      console.log('⏭️ Rule 2: Skipping version creation for new page addition');
    }

    // Rule 3: Create version for content modifications
    else if (versioningContext.isContentModification && context.hasInitialVersion) {
      console.log('📝 Rule 3: Creating version for content modification');
      createModificationVersion();
    }

    // Rule 4: Skip page navigation
    else if (versioningContext.isPageNavigation) {
      console.log('⏭️ Rule 4: Skipping version creation for page navigation');
    }

    // Rule 5: Skip if no action needed
    else {
      console.log('⏭️ Rule 5: No versioning action needed', {
        isFirstPage: versioningContext.isFirstPage,
        isContentModification: versioningContext.isContentModification,
        isNewPageAddition: versioningContext.isNewPageAddition,
        isPageNavigation: versioningContext.isPageNavigation,
        hasInitialVersion: context.hasInitialVersion,
        isFirstContentSet: context.isFirstContentSet
      });
    }

      // Update context for next iteration
      context.lastHtmlContent = htmlContent;
      context.lastPageId = currentPageId;
      context.lastPageCount = pages.length;

      // Track version switching state
      if (isVersionSwitching) {
        context.isVersionSwitching = true;
        context.lastVersionSwitchTime = Date.now();
        console.log('🔄 Version switching detected, marking context');
      } else if (context.isVersionSwitching) {
        // Clear version switching flag after a delay
        setTimeout(() => {
          context.isVersionSwitching = false;
          console.log('✅ Version switching flag cleared');
        }, 1000);
      }
    }, 100); // 100ms delay to let editor state stabilize

    return () => clearTimeout(timer);
  }, [
    prototypeId,
    htmlContent,
    currentPageId,
    pages.length,
    isGenerating,
    isVersionSwitching,
    isFirstPage,
    isContentModification,
    isPageNavigation,
    isNewPageAddition,
    createInitialVersion,
    createModificationVersion
  ]);

  /**
   * Track generation state changes for operation type detection
   */
  useEffect(() => {
    // When generation completes, create version immediately
    if (lastGenerationStateRef.current && !isGenerating && htmlContent) {
      console.log('🤖 Generation completed, creating version immediately');
      createModificationVersion(true); // immediate = true
    }
    
    lastGenerationStateRef.current = isGenerating;
  }, [isGenerating, htmlContent, createModificationVersion]);

  /**
   * Listen for version creation events
   */
  useEffect(() => {
    const handleVersionCreatedEvent = (event: CustomEvent) => {
      const { prototypeId: eventPrototypeId, versionId } = event.detail;
      if (eventPrototypeId === prototypeId) {
        handleVersionCreated(versionId);
      }
    };

    window.addEventListener('prototypeVersionCreated', handleVersionCreatedEvent as EventListener);
    
    return () => {
      window.removeEventListener('prototypeVersionCreated', handleVersionCreatedEvent as EventListener);
    };
  }, [prototypeId, handleVersionCreated]);

  /**
   * Cleanup on unmount or prototype change
   */
  useEffect(() => {
    return () => {
      if (prototypeId) {
        prototypeVersioningService.cleanup(prototypeId);
      }
    };
  }, [prototypeId]);

  /**
   * Reset context when prototype changes
   */
  useEffect(() => {
    // Only reset context when prototype ID actually changes (not on initial mount)
    const context = contextRef.current;
    const currentPrototypeId = prototypeId;

    // If this is a new prototype ID, reset the context
    if (currentPrototypeId && context.lastPageCount > 0) {
      console.log('🔄 Prototype changed, resetting versioning context');
      contextRef.current = {
        lastHtmlContent: null,
        lastPageId: null,
        lastPageCount: 0,
        hasInitialVersion: false,
        isFirstContentSet: false,
        isVersionSwitching: false,
        lastVersionSwitchTime: 0
      };
    }
  }, [prototypeId]);

  return {
    isVersionCreationInProgress: prototypeId ? prototypeVersioningService.isVersionCreationInProgress(prototypeId) : false,
    cancelPendingVersionCreation: () => {
      if (prototypeId) {
        prototypeVersioningService.cancelPendingVersionCreation(prototypeId);
      }
    }
  };
}

export default usePrototypeVersioning;
