/**
 * Hook for automatic prototype versioning integration
 * Monitors editor state and creates versions based on specific rules
 */

import { useEffect, useRef, useCallback } from 'react';
import { prototypeVersioningService } from '../services/prototypeVersioningService';

interface UsePrototypeVersioningProps {
  prototypeId: number | null;
  htmlContent: string | null;
  isGenerating: boolean;
  currentPageId: string | null;
  pages: any[];
  onVersionCreated?: (versionId: number) => void;
}

interface VersioningContext {
  lastHtmlContent: string | null;
  lastPageId: string | null;
  lastPageCount: number;
  hasInitialVersion: boolean;
  isFirstContentSet: boolean;
}

export function usePrototypeVersioning({
  prototypeId,
  htmlContent,
  isGenerating,
  currentPageId,
  pages,
  onVersionCreated
}: UsePrototypeVersioningProps) {
  const contextRef = useRef<VersioningContext>({
    lastHtmlContent: null,
    lastPageId: null,
    lastPageCount: 0,
    hasInitialVersion: false,
    isFirstContentSet: false
  });

  const lastGenerationStateRef = useRef<boolean>(false);

  /**
   * Handle version creation completion
   */
  const handleVersionCreated = useCallback((versionId: number) => {
    console.log('🎉 Version created callback:', versionId);
    onVersionCreated?.(versionId);
  }, [onVersionCreated]);

  /**
   * Check if this is the first page being created
   */
  const isFirstPage = useCallback((): boolean => {
    return pages.length === 1 && !contextRef.current.hasInitialVersion;
  }, [pages.length]);

  /**
   * Check if this is a content modification (not navigation)
   */
  const isContentModification = useCallback((): boolean => {
    const context = contextRef.current;
    
    // If page hasn't changed but content has, it's a modification
    if (context.lastPageId === currentPageId && context.lastHtmlContent !== htmlContent) {
      return true;
    }

    // If we're not generating and content changed, it's likely a manual edit
    if (!isGenerating && context.lastHtmlContent !== htmlContent && htmlContent) {
      return true;
    }

    return false;
  }, [currentPageId, htmlContent, isGenerating]);

  /**
   * Check if this is just page navigation
   */
  const isPageNavigation = useCallback((): boolean => {
    const context = contextRef.current;
    
    // Page changed but content is the same or we're just loading existing content
    return context.lastPageId !== currentPageId && context.lastHtmlContent === htmlContent;
  }, [currentPageId, htmlContent]);

  /**
   * Check if this is a new page addition
   */
  const isNewPageAddition = useCallback((): boolean => {
    const context = contextRef.current;
    return pages.length > context.lastPageCount && pages.length > 1;
  }, [pages.length]);

  /**
   * Create initial version for new prototype
   */
  const createInitialVersion = useCallback(async () => {
    if (!prototypeId || !htmlContent || contextRef.current.hasInitialVersion) {
      return;
    }

    console.log('🎯 Creating initial version for new prototype');
    
    try {
      const versionId = await prototypeVersioningService.createInitialVersion(
        prototypeId,
        htmlContent
      );
      
      if (versionId) {
        contextRef.current.hasInitialVersion = true;
        handleVersionCreated(versionId);
      }
    } catch (error) {
      console.error('❌ Failed to create initial version:', error);
    }
  }, [prototypeId, htmlContent, handleVersionCreated]);

  /**
   * Create version for content modifications
   */
  const createModificationVersion = useCallback(async (immediate = false) => {
    if (!prototypeId || !htmlContent) {
      return;
    }

    const operationType = lastGenerationStateRef.current ? 'generation' : 'manual_edit';
    const changeDescription = lastGenerationStateRef.current 
      ? 'AI-generated content update'
      : 'Manual content modification';

    console.log(`🎯 Creating ${operationType} version for content modification`);

    try {
      await prototypeVersioningService.createContentModificationVersion(
        prototypeId,
        htmlContent,
        operationType,
        {
          changeDescription,
          immediate
        }
      );
    } catch (error) {
      console.error('❌ Failed to create modification version:', error);
    }
  }, [prototypeId, htmlContent]);

  /**
   * Main versioning logic
   */
  useEffect(() => {
    if (!prototypeId) {
      return;
    }

    const context = contextRef.current;
    
    // Determine versioning context
    const versioningContext = {
      isFirstPage: isFirstPage(),
      isContentModification: isContentModification(),
      isPageNavigation: isPageNavigation(),
      isNewPageAddition: isNewPageAddition(),
      isUIStateChange: false // We don't track UI state changes in this hook
    };

    console.log('🔍 Versioning context:', {
      ...versioningContext,
      prototypeId,
      currentPageId,
      htmlContentLength: htmlContent?.length || 0,
      pagesCount: pages.length,
      isGenerating,
      hasInitialVersion: context.hasInitialVersion
    });

    // Rule 1: Create initial version for first page
    if (versioningContext.isFirstPage && htmlContent && !context.isFirstContentSet) {
      console.log('📝 Rule 1: Creating initial version for first page');
      createInitialVersion();
      context.isFirstContentSet = true;
    }
    
    // Rule 2: Skip new page additions (without content modification)
    else if (versioningContext.isNewPageAddition && !versioningContext.isContentModification) {
      console.log('⏭️ Rule 2: Skipping version creation for new page addition');
    }
    
    // Rule 3: Create version for content modifications
    else if (versioningContext.isContentModification && context.hasInitialVersion) {
      console.log('📝 Rule 3: Creating version for content modification');
      createModificationVersion();
    }
    
    // Rule 4: Skip page navigation
    else if (versioningContext.isPageNavigation) {
      console.log('⏭️ Rule 4: Skipping version creation for page navigation');
    }

    // Update context for next iteration
    context.lastHtmlContent = htmlContent;
    context.lastPageId = currentPageId;
    context.lastPageCount = pages.length;

  }, [
    prototypeId,
    htmlContent,
    currentPageId,
    pages.length,
    isFirstPage,
    isContentModification,
    isPageNavigation,
    isNewPageAddition,
    createInitialVersion,
    createModificationVersion
  ]);

  /**
   * Track generation state changes for operation type detection
   */
  useEffect(() => {
    // When generation completes, create version immediately
    if (lastGenerationStateRef.current && !isGenerating && htmlContent) {
      console.log('🤖 Generation completed, creating version immediately');
      createModificationVersion(true); // immediate = true
    }
    
    lastGenerationStateRef.current = isGenerating;
  }, [isGenerating, htmlContent, createModificationVersion]);

  /**
   * Listen for version creation events
   */
  useEffect(() => {
    const handleVersionCreatedEvent = (event: CustomEvent) => {
      const { prototypeId: eventPrototypeId, versionId } = event.detail;
      if (eventPrototypeId === prototypeId) {
        handleVersionCreated(versionId);
      }
    };

    window.addEventListener('prototypeVersionCreated', handleVersionCreatedEvent as EventListener);
    
    return () => {
      window.removeEventListener('prototypeVersionCreated', handleVersionCreatedEvent as EventListener);
    };
  }, [prototypeId, handleVersionCreated]);

  /**
   * Cleanup on unmount or prototype change
   */
  useEffect(() => {
    return () => {
      if (prototypeId) {
        prototypeVersioningService.cleanup(prototypeId);
      }
    };
  }, [prototypeId]);

  /**
   * Reset context when prototype changes
   */
  useEffect(() => {
    if (prototypeId) {
      contextRef.current = {
        lastHtmlContent: null,
        lastPageId: null,
        lastPageCount: 0,
        hasInitialVersion: false,
        isFirstContentSet: false
      };
    }
  }, [prototypeId]);

  return {
    isVersionCreationInProgress: prototypeId ? prototypeVersioningService.isVersionCreationInProgress(prototypeId) : false,
    cancelPendingVersionCreation: () => {
      if (prototypeId) {
        prototypeVersioningService.cancelPendingVersionCreation(prototypeId);
      }
    }
  };
}

export default usePrototypeVersioning;
