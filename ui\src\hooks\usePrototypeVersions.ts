/**
 * Hook for managing prototype version state and operations
 */

import { useState, useEffect, useCallback } from 'react';
import { prototypeVersionService, PrototypeVersion, VersionStats } from '../services/prototypeVersionService';

interface UsePrototypeVersionsProps {
  prototypeId: number | null;
  onVersionChange?: (version: PrototypeVersion) => void;
}

interface UsePrototypeVersionsReturn {
  // State
  versions: PrototypeVersion[];
  currentVersion: PrototypeVersion | null;
  versionLabels: string[];
  currentVersionLabel: string;
  stats: VersionStats | null;
  isLoading: boolean;
  error: string | null;

  // Actions
  loadVersionHistory: () => Promise<void>;
  switchToVersion: (versionLabel: string) => Promise<void>;
  refreshVersions: () => Promise<void>;
  clearError: () => void;
}

export function usePrototypeVersions({
  prototypeId,
  onVersionChange
}: UsePrototypeVersionsProps): UsePrototypeVersionsReturn {
  const [versions, setVersions] = useState<PrototypeVersion[]>([]);
  const [currentVersion, setCurrentVersion] = useState<PrototypeVersion | null>(null);
  const [stats, setStats] = useState<VersionStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Derived state
  const versionLabels = prototypeVersionService.formatVersionLabels(versions);
  const currentVersionLabel = currentVersion 
    ? prototypeVersionService.formatVersionLabel(currentVersion.version_number)
    : 'V1';

  /**
   * Load version history for the current prototype
   */
  const loadVersionHistory = useCallback(async () => {
    if (!prototypeId) {
      setVersions([]);
      setCurrentVersion(null);
      setStats(null);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // First load version history
      const versionHistory = await prototypeVersionService.getVersionHistory(prototypeId, 50, 0);
      setVersions(versionHistory);

      // Only load stats if versions exist to avoid errors on new prototypes
      let versionStats = null;
      if (versionHistory.length > 0) {
        try {
          versionStats = await prototypeVersionService.getVersionStats(prototypeId);
        } catch (statsError) {
          console.warn('Failed to load version stats (non-critical):', statsError);
          // Don't throw error for stats failure, it's not critical
        }
      }
      setStats(versionStats);

      // Set current version to latest if not already set
      if (versionHistory.length > 0 && !currentVersion) {
        const latestVersion = versionHistory.reduce((latest, version) =>
          version.version_number > latest.version_number ? version : latest
        );
        setCurrentVersion(latestVersion);
      }

    } catch (err) {
      console.error('Error loading version history:', err);
      setError(err instanceof Error ? err.message : 'Failed to load version history');
    } finally {
      setIsLoading(false);
    }
  }, [prototypeId, currentVersion]);

  /**
   * Switch to a specific version by label
   */
  const switchToVersion = useCallback(async (versionLabel: string) => {
    if (!prototypeId || isLoading) return;

    try {
      setIsLoading(true);
      setError(null);

      const versionNumber = prototypeVersionService.parseVersionLabel(versionLabel);
      const version = await prototypeVersionService.getVersion(prototypeId, versionNumber);

      if (version) {
        setCurrentVersion(version);
        onVersionChange?.(version);
      } else {
        throw new Error(`Version ${versionLabel} not found`);
      }

    } catch (err) {
      console.error('Error switching version:', err);
      setError(err instanceof Error ? err.message : 'Failed to switch version');
    } finally {
      setIsLoading(false);
    }
  }, [prototypeId, isLoading, onVersionChange]);

  /**
   * Refresh version data
   */
  const refreshVersions = useCallback(async () => {
    await loadVersionHistory();
  }, [loadVersionHistory]);

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Load versions when prototypeId changes, but only if we expect versions to exist
  useEffect(() => {
    if (prototypeId) {
      // Don't automatically load versions for new prototypes
      // Wait for explicit trigger (like version creation event)
      console.log('🔍 Prototype ID set, but waiting for version creation before loading history');
    }
  }, [prototypeId]);

  // Listen for version creation events to trigger initial loading
  useEffect(() => {
    const handleVersionCreated = (event: CustomEvent) => {
      const { prototypeId: eventPrototypeId } = event.detail;
      if (eventPrototypeId === prototypeId) {
        console.log('🎉 Version created event received, loading version history');
        loadVersionHistory();
      }
    };

    window.addEventListener('prototypeVersionCreated', handleVersionCreated as EventListener);

    return () => {
      window.removeEventListener('prototypeVersionCreated', handleVersionCreated as EventListener);
    };
  }, [prototypeId, loadVersionHistory]);

  // Auto-refresh versions periodically (only if we already have versions)
  useEffect(() => {
    if (!prototypeId || versions.length === 0) return;

    const interval = setInterval(() => {
      // Only refresh if not currently loading and no errors and we have versions
      if (!isLoading && !error && versions.length > 0) {
        loadVersionHistory();
      }
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [prototypeId, isLoading, error, versions.length, loadVersionHistory]);

  return {
    // State
    versions,
    currentVersion,
    versionLabels,
    currentVersionLabel,
    stats,
    isLoading,
    error,

    // Actions
    loadVersionHistory,
    switchToVersion,
    refreshVersions,
    clearError
  };
}

export default usePrototypeVersions;
