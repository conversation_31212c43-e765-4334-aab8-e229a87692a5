/**
 * Custom hook for managing prototype versioning state
 * Integrates with the existing editor state management
 */

import { useState, useCallback, useRef } from 'react';
import { versionService, VersionDetails, VersionMetadata } from '../services/versionService';

interface UseVersioningProps {
  prototypeId: number;
  onVersionChange?: (version: VersionDetails) => void;
  onVersionRestore?: (version: VersionDetails) => void;
}

interface VersioningState {
  currentVersion: VersionDetails | null;
  versions: VersionMetadata[];
  isLoadingVersions: boolean;
  isRestoringVersion: boolean;
  versionError: string | null;
  hasUnsavedChanges: boolean;
}

interface VersioningActions {
  loadVersionHistory: () => Promise<void>;
  selectVersion: (versionNumber: number) => Promise<void>;
  restoreVersion: (versionNumber: number) => Promise<void>;
  markUnsavedChanges: (hasChanges: boolean) => void;
  createVersionFromCurrentState: (htmlContent: string, operationType: string, userPrompt?: string) => Promise<void>;
  refreshVersions: () => Promise<void>;
}

export function useVersioning({ 
  prototypeId, 
  onVersionChange, 
  onVersionRestore 
}: UseVersioningProps): [VersioningState, VersioningActions] {
  
  const [state, setState] = useState<VersioningState>({
    currentVersion: null,
    versions: [],
    isLoadingVersions: false,
    isRestoringVersion: false,
    versionError: null,
    hasUnsavedChanges: false,
  });

  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const loadVersionHistory = useCallback(async () => {
    if (!prototypeId) return;

    try {
      setState(prev => ({ ...prev, isLoadingVersions: true, versionError: null }));
      
      const versions = await versionService.getVersionHistory(prototypeId, 20, 0);
      
      setState(prev => ({ 
        ...prev, 
        versions,
        isLoadingVersions: false 
      }));

      // Auto-select the latest version if none is selected
      if (versions.length > 0 && !state.currentVersion) {
        await selectVersion(versions[0].version_number);
      }
    } catch (error) {
      console.error('Error loading version history:', error);
      setState(prev => ({ 
        ...prev, 
        versionError: 'Failed to load version history',
        isLoadingVersions: false 
      }));
    }
  }, [prototypeId, state.currentVersion]);

  const selectVersion = useCallback(async (versionNumber: number) => {
    if (!prototypeId) return;

    try {
      setState(prev => ({ ...prev, isLoadingVersions: true, versionError: null }));
      
      const version = await versionService.getVersion(prototypeId, versionNumber);
      
      if (version) {
        setState(prev => ({ 
          ...prev, 
          currentVersion: version,
          isLoadingVersions: false,
          hasUnsavedChanges: false // Reset unsaved changes when switching versions
        }));
        
        onVersionChange?.(version);
      }
    } catch (error) {
      console.error('Error selecting version:', error);
      setState(prev => ({ 
        ...prev, 
        versionError: 'Failed to load version',
        isLoadingVersions: false 
      }));
    }
  }, [prototypeId, onVersionChange]);

  const restoreVersion = useCallback(async (versionNumber: number) => {
    if (!prototypeId) return;

    try {
      setState(prev => ({ ...prev, isRestoringVersion: true, versionError: null }));
      
      const version = await versionService.getVersion(prototypeId, versionNumber);
      
      if (version) {
        setState(prev => ({ 
          ...prev, 
          currentVersion: version,
          isRestoringVersion: false,
          hasUnsavedChanges: false
        }));
        
        onVersionRestore?.(version);
        
        // Refresh version history to show the restoration
        await refreshVersions();
      }
    } catch (error) {
      console.error('Error restoring version:', error);
      setState(prev => ({ 
        ...prev, 
        versionError: 'Failed to restore version',
        isRestoringVersion: false 
      }));
    }
  }, [prototypeId, onVersionRestore]);

  const markUnsavedChanges = useCallback((hasChanges: boolean) => {
    setState(prev => ({ ...prev, hasUnsavedChanges: hasChanges }));
  }, []);

  const createVersionFromCurrentState = useCallback(async (
    htmlContent: string,
    operationType: string,
    userPrompt?: string
  ) => {
    if (!prototypeId || !htmlContent) return;

    try {
      // Debounce version creation to avoid creating too many versions
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }

      debounceTimerRef.current = setTimeout(async () => {
        try {
          console.log('🔄 Creating version for prototype:', prototypeId);
          await versionService.createVersion(
            prototypeId,
            htmlContent,
            undefined, // css
            `Content updated via ${operationType}`,
            operationType,
            userPrompt
          );

          // Refresh version history to show the new version
          await refreshVersions();
          setState(prev => ({ ...prev, hasUnsavedChanges: false }));

          console.log('✅ Version created successfully');
        } catch (error) {
          console.error('❌ Error creating version:', error);
          setState(prev => ({
            ...prev,
            versionError: 'Failed to create version'
          }));
        }
      }, 3000); // Wait 3 seconds after last change

    } catch (error) {
      console.error('Error setting up version creation:', error);
    }
  }, [prototypeId, refreshVersions]);

  const refreshVersions = useCallback(async () => {
    await loadVersionHistory();
  }, [loadVersionHistory]);

  const actions: VersioningActions = {
    loadVersionHistory,
    selectVersion,
    restoreVersion,
    markUnsavedChanges,
    createVersionFromCurrentState,
    refreshVersions,
  };

  return [state, actions];
}
