/**
 * Prototype Version Service
 * Handles prototype version management and API calls
 */

export interface PrototypeVersion {
  id: number;
  prototype_id: number;
  version_number: number;
  html: string;
  css?: string;
  change_description?: string;
  operation_type: 'generation' | 'manual_edit' | 'restoration';
  user_prompt?: string;
  llm_response?: string;
  created_at: string;
}

export interface VersionStats {
  total_versions: number;
  latest_version: number;
  first_created: string;
  last_updated: string;
}

class PrototypeVersionService {
  private baseUrl = '/api/llm/v3';

  /**
   * Get version history for a prototype
   */
  async getVersionHistory(prototypeId: number, limit = 20, offset = 0): Promise<PrototypeVersion[]> {
    try {
      const response = await fetch(
        `${this.baseUrl}/versions/${prototypeId}?limit=${limit}&offset=${offset}`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch version history: ${response.statusText}`);
      }

      const data = await response.json();
      return data.versions || [];
    } catch (error) {
      console.error('Error fetching version history:', error);
      throw error;
    }
  }

  /**
   * Get a specific version
   */
  async getVersion(prototypeId: number, versionNumber: number): Promise<PrototypeVersion | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/versions/${prototypeId}/${versionNumber}`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch version: ${response.statusText}`);
      }

      const data = await response.json();
      return data.version || null;
    } catch (error) {
      console.error('Error fetching version:', error);
      throw error;
    }
  }

  /**
   * Get version statistics
   */
  async getVersionStats(prototypeId: number): Promise<VersionStats | null> {
    try {
      const response = await fetch(
        `${this.baseUrl}/versions/${prototypeId}/stats`,
        {
          method: 'GET',
          credentials: 'include',
          headers: {
            'Content-Type': 'application/json',
          },
        }
      );

      if (!response.ok) {
        throw new Error(`Failed to fetch version stats: ${response.statusText}`);
      }

      const data = await response.json();
      return data.stats || null;
    } catch (error) {
      console.error('Error fetching version stats:', error);
      throw error;
    }
  }

  /**
   * Convert version numbers to display labels
   */
  formatVersionLabel(versionNumber: number): string {
    return `V${versionNumber}`;
  }

  /**
   * Convert version list to display labels
   */
  formatVersionLabels(versions: PrototypeVersion[]): string[] {
    return versions
      .sort((a, b) => a.version_number - b.version_number)
      .map(v => this.formatVersionLabel(v.version_number));
  }

  /**
   * Get operation icon for version type
   */
  getOperationIcon(operationType: string): string {
    switch (operationType) {
      case 'generation':
        return '🤖';
      case 'manual_edit':
        return '✏️';
      case 'restoration':
        return '🔄';
      default:
        return '📝';
    }
  }

  /**
   * Get operation name for display
   */
  getOperationName(operationType: string): string {
    switch (operationType) {
      case 'generation':
        return 'Generated';
      case 'manual_edit':
        return 'Manual Edit';
      case 'restoration':
        return 'Restored';
      default:
        return 'Unknown';
    }
  }

  /**
   * Format relative time for display
   */
  formatRelativeTime(dateString: string): string {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 1) {
      return 'Just now';
    } else if (diffMins < 60) {
      return `${diffMins} minute${diffMins === 1 ? '' : 's'} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
    } else {
      return date.toLocaleDateString();
    }
  }

  /**
   * Parse version label back to number
   */
  parseVersionLabel(label: string): number {
    const match = label.match(/^V(\d+)$/);
    return match ? parseInt(match[1], 10) : 1;
  }

  /**
   * Find version by label in version list
   */
  findVersionByLabel(versions: PrototypeVersion[], label: string): PrototypeVersion | null {
    const versionNumber = this.parseVersionLabel(label);
    return versions.find(v => v.version_number === versionNumber) || null;
  }
}

// Export singleton instance
export const prototypeVersionService = new PrototypeVersionService();
export default prototypeVersionService;
