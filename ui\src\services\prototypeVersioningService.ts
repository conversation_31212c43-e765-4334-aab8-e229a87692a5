/**
 * Prototype Versioning Service
 * Handles automatic version creation based on specific rules and triggers
 */

import { prototypeVersionService } from './prototypeVersionService';

export interface VersionCreationContext {
  prototypeId: number;
  htmlContent: string;
  operationType: 'generation' | 'manual_edit' | 'restoration';
  changeDescription?: string;
  userPrompt?: string;
  llmResponse?: string;
  css?: string;
}

export interface VersioningState {
  isCreatingVersion: boolean;
  lastVersionCreated: number | null;
  pendingVersionCreation: boolean;
  debounceTimer: NodeJS.Timeout | null;
}

class PrototypeVersioningService {
  private versioningStates = new Map<number, VersioningState>();
  private readonly DEBOUNCE_DELAY = 2500; // 2.5 seconds
  private readonly VERSION_CREATION_ENDPOINT = '/api/llm/v3/versions';

  /**
   * Get or initialize versioning state for a prototype
   */
  private getVersioningState(prototypeId: number): VersioningState {
    if (!this.versioningStates.has(prototypeId)) {
      this.versioningStates.set(prototypeId, {
        isCreatingVersion: false,
        lastVersionCreated: null,
        pendingVersionCreation: false,
        debounceTimer: null
      });
    }
    return this.versioningStates.get(prototypeId)!;
  }

  /**
   * Check if this is the first version for a prototype
   */
  async isFirstVersion(prototypeId: number): Promise<boolean> {
    try {
      const versions = await prototypeVersionService.getVersionHistory(prototypeId, 1, 0);
      return versions.length === 0;
    } catch (error) {
      console.warn('Error checking if first version (assuming true for new prototype):', error);
      // If we can't check version history, assume it's a new prototype
      return true;
    }
  }

  /**
   * Create a new prototype version via API
   */
  private async createVersionAPI(context: VersionCreationContext): Promise<number | null> {
    try {
      const response = await fetch(`${this.VERSION_CREATION_ENDPOINT}/${context.prototypeId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          html: context.htmlContent,
          css: context.css || null,
          change_description: context.changeDescription || null,
          operation_type: context.operationType,
          user_prompt: context.userPrompt || null,
          llm_response: context.llmResponse || null
        })
      });

      if (!response.ok) {
        throw new Error(`Version creation failed: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('✅ Prototype version created:', result);
      return result.versionId || null;
    } catch (error) {
      console.error('❌ Error creating prototype version:', error);
      throw error;
    }
  }

  /**
   * Create initial version for new prototype (V1)
   */
  async createInitialVersion(prototypeId: number, htmlContent: string): Promise<number | null> {
    console.log('🎯 Creating initial prototype version V1 for prototype:', prototypeId);
    
    const state = this.getVersioningState(prototypeId);
    
    if (state.isCreatingVersion) {
      console.log('⏳ Version creation already in progress, skipping...');
      return null;
    }

    try {
      state.isCreatingVersion = true;

      const versionId = await this.createVersionAPI({
        prototypeId,
        htmlContent,
        operationType: 'generation',
        changeDescription: 'Initial prototype version'
      });

      if (versionId) {
        state.lastVersionCreated = versionId;
        console.log('✅ Initial version V1 created successfully');
      }

      return versionId;
    } catch (error) {
      console.error('❌ Failed to create initial version:', error);
      return null;
    } finally {
      state.isCreatingVersion = false;
    }
  }

  /**
   * Create version for content modification (debounced)
   */
  async createContentModificationVersion(
    prototypeId: number,
    htmlContent: string,
    operationType: 'generation' | 'manual_edit' = 'manual_edit',
    options: {
      changeDescription?: string;
      userPrompt?: string;
      llmResponse?: string;
      css?: string;
      immediate?: boolean;
    } = {}
  ): Promise<void> {
    const state = this.getVersioningState(prototypeId);
    
    // Clear existing debounce timer
    if (state.debounceTimer) {
      clearTimeout(state.debounceTimer);
    }

    // Skip if already creating a version
    if (state.isCreatingVersion) {
      console.log('⏳ Version creation in progress, skipping content modification version');
      return;
    }

    const createVersion = async () => {
      try {
        state.isCreatingVersion = true;
        state.pendingVersionCreation = false;

        console.log(`🎯 Creating content modification version for prototype ${prototypeId}`);

        const versionId = await this.createVersionAPI({
          prototypeId,
          htmlContent,
          operationType,
          changeDescription: options.changeDescription || `Content ${operationType === 'generation' ? 'generated' : 'modified'}`,
          userPrompt: options.userPrompt,
          llmResponse: options.llmResponse,
          css: options.css
        });

        if (versionId) {
          state.lastVersionCreated = versionId;
          console.log('✅ Content modification version created successfully');
          
          // Emit custom event for UI updates
          window.dispatchEvent(new CustomEvent('prototypeVersionCreated', {
            detail: { prototypeId, versionId, operationType }
          }));
        }
      } catch (error) {
        console.error('❌ Failed to create content modification version:', error);
      } finally {
        state.isCreatingVersion = false;
      }
    };

    if (options.immediate) {
      await createVersion();
    } else {
      // Debounced version creation
      state.pendingVersionCreation = true;
      state.debounceTimer = setTimeout(createVersion, this.DEBOUNCE_DELAY);
      console.log(`⏱️ Debounced version creation scheduled (${this.DEBOUNCE_DELAY}ms)`);
    }
  }

  /**
   * Cancel pending version creation
   */
  cancelPendingVersionCreation(prototypeId: number): void {
    const state = this.getVersioningState(prototypeId);
    
    if (state.debounceTimer) {
      clearTimeout(state.debounceTimer);
      state.debounceTimer = null;
    }
    
    if (state.pendingVersionCreation) {
      state.pendingVersionCreation = false;
      console.log('🚫 Cancelled pending version creation');
    }
  }

  /**
   * Check if version creation is in progress
   */
  isVersionCreationInProgress(prototypeId: number): boolean {
    const state = this.getVersioningState(prototypeId);
    return state.isCreatingVersion || state.pendingVersionCreation;
  }

  /**
   * Get the last created version ID
   */
  getLastCreatedVersion(prototypeId: number): number | null {
    const state = this.getVersioningState(prototypeId);
    return state.lastVersionCreated;
  }

  /**
   * Clean up versioning state for a prototype
   */
  cleanup(prototypeId: number): void {
    const state = this.getVersioningState(prototypeId);
    
    if (state.debounceTimer) {
      clearTimeout(state.debounceTimer);
    }
    
    this.versioningStates.delete(prototypeId);
    console.log(`🧹 Cleaned up versioning state for prototype ${prototypeId}`);
  }

  /**
   * Determine if a version should be created based on context
   */
  shouldCreateVersion(context: {
    isFirstPage: boolean;
    isContentModification: boolean;
    isPageNavigation: boolean;
    isNewPageAddition: boolean;
    isUIStateChange: boolean;
  }): boolean {
    // Create version for first page (initial version)
    if (context.isFirstPage) {
      return true;
    }

    // Create version for content modifications
    if (context.isContentModification && !context.isPageNavigation && !context.isUIStateChange) {
      return true;
    }

    // Do NOT create versions for:
    // - New page additions (without content modification)
    // - Page navigation
    // - UI state changes
    if (context.isNewPageAddition || context.isPageNavigation || context.isUIStateChange) {
      return false;
    }

    return false;
  }
}

// Export singleton instance
export const prototypeVersioningService = new PrototypeVersioningService();
export default prototypeVersioningService;
