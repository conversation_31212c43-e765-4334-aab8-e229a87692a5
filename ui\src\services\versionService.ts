/**
 * Frontend service for prototype version management
 * Integrates with the existing backend versioning system
 */

export interface VersionMetadata {
  id: number;
  version_number: number;
  change_description: string | null;
  operation_type: 'generate' | 'edit' | 'implement' | 'session_edit';
  created_at: string;
}

export interface VersionDetails extends VersionMetadata {
  html: string;
  css: string | null;
  user_prompt: string | null;
  llm_response: string | null;
}

export interface VersionStats {
  total_versions: number;
  first_version_date: string;
  latest_version_date: string;
  operation_types_used: number;
  operation_breakdown: Array<{
    operation_type: string;
    count: number;
  }>;
}

class VersionService {
  private baseUrl = '/api/llm/v3/versions';

  /**
   * Get version history for a prototype
   */
  async getVersionHistory(prototypeId: number, limit = 10, offset = 0): Promise<VersionMetadata[]> {
    try {
      const response = await fetch(`${this.baseUrl}/${prototypeId}?limit=${limit}&offset=${offset}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch version history: ${response.statusText}`);
      }

      const data = await response.json();
      return data.versions || [];
    } catch (error) {
      console.error('Error fetching version history:', error);
      throw error;
    }
  }

  /**
   * Get specific version details
   */
  async getVersion(prototypeId: number, versionNumber: number): Promise<VersionDetails | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${prototypeId}/${versionNumber}`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          return null;
        }
        throw new Error(`Failed to fetch version: ${response.statusText}`);
      }

      const data = await response.json();
      return data.version || null;
    } catch (error) {
      console.error('Error fetching version:', error);
      throw error;
    }
  }

  /**
   * Get version statistics for a prototype
   */
  async getVersionStats(prototypeId: number): Promise<VersionStats | null> {
    try {
      const response = await fetch(`${this.baseUrl}/${prototypeId}/stats`, {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch version stats: ${response.statusText}`);
      }

      const data = await response.json();
      return data.stats || null;
    } catch (error) {
      console.error('Error fetching version stats:', error);
      throw error;
    }
  }

  /**
   * Format relative time for version timestamps
   */
  formatRelativeTime(timestamp: string): string {
    const now = new Date();
    const versionTime = new Date(timestamp);
    const diffMs = now.getTime() - versionTime.getTime();
    
    const diffMinutes = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMinutes < 1) {
      return 'Just now';
    } else if (diffMinutes < 60) {
      return `${diffMinutes} minute${diffMinutes === 1 ? '' : 's'} ago`;
    } else if (diffHours < 24) {
      return `${diffHours} hour${diffHours === 1 ? '' : 's'} ago`;
    } else if (diffDays < 7) {
      return `${diffDays} day${diffDays === 1 ? '' : 's'} ago`;
    } else {
      return versionTime.toLocaleDateString();
    }
  }

  /**
   * Get icon for operation type
   */
  getOperationIcon(operationType: string): string {
    switch (operationType) {
      case 'generate':
        return '🤖';
      case 'edit':
      case 'session_edit':
        return '✏️';
      case 'implement':
        return '🔧';
      default:
        return '📝';
    }
  }

  /**
   * Get human-readable operation name
   */
  getOperationName(operationType: string): string {
    switch (operationType) {
      case 'generate':
        return 'Generated';
      case 'edit':
        return 'Manual Edit';
      case 'session_edit':
        return 'Session Edit';
      case 'implement':
        return 'Implementation';
      default:
        return 'Modified';
    }
  }

  /**
   * Create a new version
   */
  async createVersion(
    prototypeId: number,
    html: string,
    css?: string,
    changeDescription?: string,
    operationType: string = 'manual_edit',
    userPrompt?: string,
    llmResponse?: string
  ): Promise<number> {
    try {
      const response = await fetch(`${this.baseUrl}/${prototypeId}`, {
        method: 'POST',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          html,
          css,
          changeDescription,
          operationType,
          userPrompt,
          llmResponse,
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to create version: ${response.statusText}`);
      }

      const data = await response.json();
      return data.versionId;
    } catch (error) {
      console.error('Error creating version:', error);
      throw error;
    }
  }
}

export const versionService = new VersionService();
