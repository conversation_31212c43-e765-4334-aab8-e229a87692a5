/**
 * Integration tests for the versioning system
 * Tests the integration between VersionSelector, useVersioning hook, and versionService
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useVersioning } from '../hooks/useVersioning';
import { versionService } from '../services/versionService';

// Mock the version service
vi.mock('../services/versionService', () => ({
  versionService: {
    getVersionHistory: vi.fn(),
    getVersion: vi.fn(),
    createVersion: vi.fn(),
    formatRelativeTime: vi.fn(),
    getOperationIcon: vi.fn(),
    getOperationName: vi.fn(),
  }
}));

describe('Versioning Integration', () => {
  const mockPrototypeId = 123;
  const mockVersions = [
    {
      id: 1,
      version_number: 3,
      change_description: 'Latest changes',
      operation_type: 'edit' as const,
      created_at: '2024-01-01T12:00:00Z'
    },
    {
      id: 2,
      version_number: 2,
      change_description: 'Generated content',
      operation_type: 'generate' as const,
      created_at: '2024-01-01T11:00:00Z'
    },
    {
      id: 3,
      version_number: 1,
      change_description: 'Initial version',
      operation_type: 'generate' as const,
      created_at: '2024-01-01T10:00:00Z'
    }
  ];

  const mockVersionDetails = {
    ...mockVersions[0],
    html: '<div>Test HTML content</div>',
    css: null,
    user_prompt: 'Test prompt',
    llm_response: 'Test response'
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup default mock implementations
    (versionService.getVersionHistory as any).mockResolvedValue(mockVersions);
    (versionService.getVersion as any).mockResolvedValue(mockVersionDetails);
    (versionService.createVersion as any).mockResolvedValue(4);
    (versionService.formatRelativeTime as any).mockReturnValue('2 minutes ago');
    (versionService.getOperationIcon as any).mockReturnValue('✏️');
    (versionService.getOperationName as any).mockReturnValue('Manual Edit');
  });

  describe('useVersioning Hook', () => {
    it('should initialize with empty state', () => {
      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId
      }));

      const [state] = result.current;

      expect(state.currentVersion).toBeNull();
      expect(state.versions).toEqual([]);
      expect(state.isLoadingVersions).toBe(false);
      expect(state.isRestoringVersion).toBe(false);
      expect(state.versionError).toBeNull();
      expect(state.hasUnsavedChanges).toBe(false);
    });

    it('should load version history', async () => {
      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId
      }));

      const [, actions] = result.current;

      await act(async () => {
        await actions.loadVersionHistory();
      });

      expect(versionService.getVersionHistory).toHaveBeenCalledWith(mockPrototypeId, 20, 0);
      expect(result.current[0].versions).toEqual(mockVersions);
    });

    it('should select a version', async () => {
      const mockOnVersionChange = vi.fn();
      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId,
        onVersionChange: mockOnVersionChange
      }));

      const [, actions] = result.current;

      await act(async () => {
        await actions.selectVersion(3);
      });

      expect(versionService.getVersion).toHaveBeenCalledWith(mockPrototypeId, 3);
      expect(mockOnVersionChange).toHaveBeenCalledWith(mockVersionDetails);
      expect(result.current[0].currentVersion).toEqual(mockVersionDetails);
    });

    it('should restore a version', async () => {
      const mockOnVersionRestore = vi.fn();
      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId,
        onVersionRestore: mockOnVersionRestore
      }));

      const [, actions] = result.current;

      await act(async () => {
        await actions.restoreVersion(2);
      });

      expect(versionService.getVersion).toHaveBeenCalledWith(mockPrototypeId, 2);
      expect(mockOnVersionRestore).toHaveBeenCalledWith(mockVersionDetails);
      expect(result.current[0].currentVersion).toEqual(mockVersionDetails);
    });

    it('should mark unsaved changes', () => {
      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId
      }));

      const [, actions] = result.current;

      act(() => {
        actions.markUnsavedChanges(true);
      });

      expect(result.current[0].hasUnsavedChanges).toBe(true);

      act(() => {
        actions.markUnsavedChanges(false);
      });

      expect(result.current[0].hasUnsavedChanges).toBe(false);
    });

    it('should handle version creation with debouncing', async () => {
      vi.useFakeTimers();
      
      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId
      }));

      const [, actions] = result.current;

      // Call createVersionFromCurrentState multiple times
      act(() => {
        actions.createVersionFromCurrentState('<div>Content 1</div>', 'edit', 'Test prompt 1');
        actions.createVersionFromCurrentState('<div>Content 2</div>', 'edit', 'Test prompt 2');
        actions.createVersionFromCurrentState('<div>Content 3</div>', 'edit', 'Test prompt 3');
      });

      // Fast-forward time to trigger debounced function
      await act(async () => {
        vi.advanceTimersByTime(3000);
      });

      // Should only create one version (the last one due to debouncing)
      expect(versionService.createVersion).toHaveBeenCalledTimes(1);
      expect(versionService.createVersion).toHaveBeenCalledWith(
        mockPrototypeId,
        '<div>Content 3</div>',
        undefined,
        'Content updated via edit',
        'edit',
        'Test prompt 3'
      );

      vi.useRealTimers();
    });
  });

  describe('Version Service Integration', () => {
    it('should format relative time correctly', () => {
      const timestamp = '2024-01-01T12:00:00Z';
      versionService.formatRelativeTime(timestamp);
      
      expect(versionService.formatRelativeTime).toHaveBeenCalledWith(timestamp);
    });

    it('should get operation icons correctly', () => {
      versionService.getOperationIcon('generate');
      versionService.getOperationIcon('edit');
      versionService.getOperationIcon('implement');
      
      expect(versionService.getOperationIcon).toHaveBeenCalledTimes(3);
    });

    it('should get operation names correctly', () => {
      versionService.getOperationName('generate');
      versionService.getOperationName('edit');
      versionService.getOperationName('implement');
      
      expect(versionService.getOperationName).toHaveBeenCalledTimes(3);
    });
  });

  describe('Error Handling', () => {
    it('should handle version loading errors', async () => {
      const error = new Error('Failed to load versions');
      (versionService.getVersionHistory as any).mockRejectedValue(error);

      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId
      }));

      const [, actions] = result.current;

      await act(async () => {
        await actions.loadVersionHistory();
      });

      expect(result.current[0].versionError).toBe('Failed to load version history');
    });

    it('should handle version selection errors', async () => {
      const error = new Error('Failed to load version');
      (versionService.getVersion as any).mockRejectedValue(error);

      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId
      }));

      const [, actions] = result.current;

      await act(async () => {
        await actions.selectVersion(999);
      });

      expect(result.current[0].versionError).toBe('Failed to load version');
    });

    it('should handle version creation errors', async () => {
      vi.useFakeTimers();
      
      const error = new Error('Failed to create version');
      (versionService.createVersion as any).mockRejectedValue(error);

      const { result } = renderHook(() => useVersioning({
        prototypeId: mockPrototypeId
      }));

      const [, actions] = result.current;

      act(() => {
        actions.createVersionFromCurrentState('<div>Test</div>', 'edit');
      });

      await act(async () => {
        vi.advanceTimersByTime(3000);
      });

      expect(result.current[0].versionError).toBe('Failed to create version');

      vi.useRealTimers();
    });
  });
});
